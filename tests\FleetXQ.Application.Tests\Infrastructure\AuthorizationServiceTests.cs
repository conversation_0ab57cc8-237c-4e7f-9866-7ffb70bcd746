using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Interfaces;
using FleetXQ.Infrastructure.Authentication;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using Xunit;

namespace FleetXQ.Application.Tests.Infrastructure;

/// <summary>
/// Unit tests for AuthorizationService
/// </summary>
public sealed class AuthorizationServiceTests
{
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<ILogger<AuthorizationService>> _mockLogger;
    private readonly AuthorizationService _authorizationService;

    public AuthorizationServiceTests()
    {
        _mockUserRepository = new Mock<IUserRepository>();
        _mockLogger = new Mock<ILogger<AuthorizationService>>();
        _authorizationService = new AuthorizationService(_mockUserRepository.Object, _mockLogger.Object);
    }

    [Theory]
    [InlineData(UserRole.Admin, UserRole.User, true)]
    [InlineData(UserRole.Admin, UserRole.Admin, true)]
    [InlineData(UserRole.Manager, UserRole.User, true)]
    [InlineData(UserRole.Manager, UserRole.Manager, true)]
    [InlineData(UserRole.Manager, UserRole.Admin, false)]
    [InlineData(UserRole.User, UserRole.Admin, false)]
    [InlineData(UserRole.Driver, UserRole.Manager, false)]
    public async Task HasRoleAsync_DifferentRoles_ReturnsExpectedResult(UserRole userRole, UserRole requiredRole, bool expectedResult)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", userRole);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _authorizationService.HasRoleAsync(userId, requiredRole);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task HasRoleAsync_UserNotFound_ReturnsFalse()
    {
        // Arrange
        var userId = Guid.NewGuid();

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _authorizationService.HasRoleAsync(userId, UserRole.User);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task HasRoleAsync_InactiveUser_ReturnsFalse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.Admin);
        user.Deactivate();
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _authorizationService.HasRoleAsync(userId, UserRole.User);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task HasRoleAsync_LockedOutUser_ReturnsFalse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.Admin);
        // Simulate lockout
        for (int i = 0; i < 5; i++)
        {
            user.RecordFailedLogin();
        }
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _authorizationService.HasRoleAsync(userId, UserRole.User);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData(UserRole.Admin, "vehicles.read", true)]
    [InlineData(UserRole.Admin, "system.admin", true)]
    [InlineData(UserRole.Manager, "vehicles.read", true)]
    [InlineData(UserRole.Manager, "system.admin", false)]
    [InlineData(UserRole.Driver, "vehicles.read", true)]
    [InlineData(UserRole.Driver, "users.read", false)]
    [InlineData(UserRole.User, "vehicles.read", true)]
    [InlineData(UserRole.User, "vehicles.write", false)]
    public async Task HasPermissionAsync_DifferentRolesAndPermissions_ReturnsExpectedResult(UserRole userRole, string permission, bool expectedResult)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", userRole);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _authorizationService.HasPermissionAsync(userId, permission);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task GetUserPermissionsAsync_AdminUser_ReturnsAllPermissions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.Admin);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var permissions = await _authorizationService.GetUserPermissionsAsync(userId);

        // Assert
        permissions.Should().NotBeEmpty();
        permissions.Should().Contain("vehicles.read");
        permissions.Should().Contain("vehicles.write");
        permissions.Should().Contain("vehicles.delete");
        permissions.Should().Contain("users.read");
        permissions.Should().Contain("users.write");
        permissions.Should().Contain("users.delete");
        permissions.Should().Contain("system.admin");
    }

    [Fact]
    public async Task GetUserPermissionsAsync_DriverUser_ReturnsLimitedPermissions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.Driver);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var permissions = await _authorizationService.GetUserPermissionsAsync(userId);

        // Assert
        permissions.Should().NotBeEmpty();
        permissions.Should().Contain("vehicles.read");
        permissions.Should().Contain("profile.read");
        permissions.Should().Contain("profile.write");
        permissions.Should().NotContain("vehicles.write");
        permissions.Should().NotContain("users.read");
        permissions.Should().NotContain("system.admin");
    }

    [Fact]
    public async Task CanAccessResourceAsync_AdminUser_CanAccessAllResources()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.Admin);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var canAccess = await _authorizationService.CanAccessResourceAsync(userId, "vehicles", Guid.NewGuid(), "read");

        // Assert
        canAccess.Should().BeTrue();
    }

    [Fact]
    public async Task CanAccessResourceAsync_UserWithoutPermission_CannotAccessResource()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.User);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var canAccess = await _authorizationService.CanAccessResourceAsync(userId, "vehicles", Guid.NewGuid(), "write");

        // Assert
        canAccess.Should().BeFalse();
    }

    [Fact]
    public void ValidateUserContext_ValidClaims_ReturnsUserContext()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId.ToString()),
            new(ClaimTypes.Name, "testuser"),
            new(ClaimTypes.Role, "Admin"),
            new("is_active", "True")
        };
        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(claims));

        // Act
        var userContext = _authorizationService.ValidateUserContext(claimsPrincipal);

        // Assert
        userContext.Should().NotBeNull();
        userContext!.UserId.Should().Be(userId);
        userContext.Username.Should().Be("testuser");
        userContext.Role.Should().Be(UserRole.Admin);
        userContext.IsActive.Should().BeTrue();
        userContext.Permissions.Should().NotBeEmpty();
    }

    [Fact]
    public void ValidateUserContext_MissingRequiredClaims_ReturnsNull()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new(ClaimTypes.Name, "testuser")
            // Missing NameIdentifier and Role claims
        };
        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(claims));

        // Act
        var userContext = _authorizationService.ValidateUserContext(claimsPrincipal);

        // Assert
        userContext.Should().BeNull();
    }

    [Fact]
    public void ValidateUserContext_InvalidUserIdFormat_ReturnsNull()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, "invalid-guid"),
            new(ClaimTypes.Name, "testuser"),
            new(ClaimTypes.Role, "Admin")
        };
        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(claims));

        // Act
        var userContext = _authorizationService.ValidateUserContext(claimsPrincipal);

        // Assert
        userContext.Should().BeNull();
    }

    [Fact]
    public void ValidateUserContext_InvalidRoleFormat_ReturnsNull()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId.ToString()),
            new(ClaimTypes.Name, "testuser"),
            new(ClaimTypes.Role, "InvalidRole")
        };
        var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(claims));

        // Act
        var userContext = _authorizationService.ValidateUserContext(claimsPrincipal);

        // Assert
        userContext.Should().BeNull();
    }

    [Fact]
    public async Task HasRoleAsync_ExceptionThrown_ReturnsFalse()
    {
        // Arrange
        var userId = Guid.NewGuid();

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _authorizationService.HasRoleAsync(userId, UserRole.User);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task HasPermissionAsync_ExceptionThrown_ReturnsFalse()
    {
        // Arrange
        var userId = Guid.NewGuid();

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _authorizationService.HasPermissionAsync(userId, "vehicles.read");

        // Assert
        result.Should().BeFalse();
    }
}
