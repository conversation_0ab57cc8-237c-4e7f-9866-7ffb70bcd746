using FleetXQ.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;

namespace FleetXQ.Api.Hubs;

/// <summary>
/// Base hub class providing common functionality for all FleetXQ SignalR hubs
/// </summary>
[Authorize]
public abstract class BaseFleetXQHub : Hub
{
    protected readonly ILogger Logger;
    protected readonly SignalRConnectionStateService? ConnectionStateService;

    protected BaseFleetXQHub(ILogger logger, SignalRConnectionStateService? connectionStateService = null)
    {
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        ConnectionStateService = connectionStateService;
    }

    /// <summary>
    /// Gets the current user ID from the connection context
    /// </summary>
    protected Guid GetCurrentUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("User ID not found in connection context");
        }
        return userId;
    }

    /// <summary>
    /// Gets the current user's roles from the connection context
    /// </summary>
    protected IEnumerable<string> GetCurrentUserRoles()
    {
        return Context.User?.FindAll(ClaimTypes.Role)?.Select(c => c.Value) ?? Enumerable.Empty<string>();
    }

    /// <summary>
    /// Checks if the current user has the specified role
    /// </summary>
    protected bool HasRole(string role)
    {
        return GetCurrentUserRoles().Contains(role, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Gets the connection ID for the current context
    /// </summary>
    protected string GetConnectionId()
    {
        return Context.ConnectionId;
    }

    /// <summary>
    /// Called when a connection is established
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRoles = GetCurrentUserRoles();
            var connectionId = GetConnectionId();
            var userAgent = Context.GetHttpContext()?.Request.Headers.UserAgent.ToString() ?? "Unknown";
            var ipAddress = Context.GetHttpContext()?.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

            Logger.LogInformation("User {UserId} connected to {HubName} with roles: {Roles}",
                userId, GetType().Name, string.Join(", ", userRoles));

            // Record connection state if service is available
            if (ConnectionStateService != null)
            {
                await ConnectionStateService.OnConnectionEstablishedAsync(
                    connectionId, userId, GetType().Name, userAgent, ipAddress);
            }

            await base.OnConnectedAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during connection establishment for {HubName}", GetType().Name);
            throw;
        }
    }

    /// <summary>
    /// Called when a connection is terminated
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        try
        {
            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();

            if (exception != null)
            {
                Logger.LogWarning(exception, "User {UserId} disconnected from {HubName} with exception",
                    userId, GetType().Name);
            }
            else
            {
                Logger.LogInformation("User {UserId} disconnected from {HubName}",
                    userId, GetType().Name);
            }

            // Record connection termination if service is available
            if (ConnectionStateService != null)
            {
                await ConnectionStateService.OnConnectionTerminatedAsync(connectionId, exception);
            }

            await base.OnDisconnectedAsync(exception);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during disconnection handling for {HubName}", GetType().Name);
        }
    }

    /// <summary>
    /// Safely executes a hub method with error handling and logging
    /// </summary>
    protected async Task<T> ExecuteSafelyAsync<T>(Func<Task<T>> operation, string operationName)
    {
        try
        {
            // Update connection activity
            ConnectionStateService?.UpdateConnectionActivity(GetConnectionId());

            Logger.LogDebug("Executing {OperationName} for user {UserId}", operationName, GetCurrentUserId());
            var result = await operation();
            Logger.LogDebug("Successfully executed {OperationName} for user {UserId}", operationName, GetCurrentUserId());
            return result;
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized access during {OperationName} for user {UserId}", 
                operationName, GetCurrentUserId());
            await Clients.Caller.SendAsync("Error", new { Type = "Unauthorized", Message = ex.Message });
            throw;
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument during {OperationName} for user {UserId}", 
                operationName, GetCurrentUserId());
            await Clients.Caller.SendAsync("Error", new { Type = "InvalidArgument", Message = ex.Message });
            throw;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during {OperationName} for user {UserId}", 
                operationName, GetCurrentUserId());
            await Clients.Caller.SendAsync("Error", new { Type = "InternalError", Message = "An internal error occurred" });
            throw;
        }
    }

    /// <summary>
    /// Safely executes a hub method with error handling and logging (void return)
    /// </summary>
    protected async Task ExecuteSafelyAsync(Func<Task> operation, string operationName)
    {
        await ExecuteSafelyAsync(async () =>
        {
            await operation();
            return true;
        }, operationName);
    }
}
