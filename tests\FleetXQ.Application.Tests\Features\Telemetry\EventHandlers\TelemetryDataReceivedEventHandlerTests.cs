using AutoMapper;
using FleetXQ.Application.Features.Telemetry.EventHandlers;
using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Events;
using FleetXQ.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace FleetXQ.Application.Tests.Features.Telemetry.EventHandlers;

public class TelemetryDataReceivedEventHandlerTests
{
    private readonly Mock<ISignalRNotificationService> _mockSignalRService;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<TelemetryDataReceivedEventHandler>> _mockLogger;
    private readonly TelemetryDataReceivedEventHandler _handler;

    public TelemetryDataReceivedEventHandlerTests()
    {
        _mockSignalRService = new Mock<ISignalRNotificationService>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<TelemetryDataReceivedEventHandler>>();

        _handler = new TelemetryDataReceivedEventHandler(
            _mockSignalRService.Object,
            _mockMapper.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidEvent_ShouldSendTelemetryAndStatusUpdates()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var fuelLevel = new FuelLevel(75);
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, fuelLevel, 1000m, timestamp);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSignalRService.Verify(s => s.SendTelemetryUpdateAsync(
            vehicleId, 
            It.IsAny<TelemetryDataDto>(), 
            It.IsAny<CancellationToken>()), Times.Once);

        _mockSignalRService.Verify(s => s.SendVehicleStatusUpdateAsync(
            vehicleId,
            "Moving", // Speed > 0
            timestamp,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithZeroSpeed_ShouldDetermineIdleStatus()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(0); // Zero speed
        var fuelLevel = new FuelLevel(75);
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, fuelLevel, 1000m, timestamp);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSignalRService.Verify(s => s.SendVehicleStatusUpdateAsync(
            vehicleId,
            "Idle", // Speed = 0 and recent data
            timestamp,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithLowFuelLevel_ShouldDetermineLowFuelStatus()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(0);
        var fuelLevel = new FuelLevel(5); // Low fuel
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, fuelLevel, 1000m, timestamp);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSignalRService.Verify(s => s.SendVehicleStatusUpdateAsync(
            vehicleId,
            "Low Fuel", // Low fuel takes precedence over idle
            timestamp,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithOldTimestamp_ShouldDetermineOfflineStatus()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(0);
        var fuelLevel = new FuelLevel(75);
        var timestamp = DateTime.UtcNow.AddHours(-1); // Old timestamp

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, fuelLevel, 1000m, timestamp);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSignalRService.Verify(s => s.SendVehicleStatusUpdateAsync(
            vehicleId,
            "Offline", // Data is too old
            timestamp,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNullFuelLevel_ShouldHandleGracefully()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(30);
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, null, 1000m, timestamp);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSignalRService.Verify(s => s.SendTelemetryUpdateAsync(
            vehicleId, 
            It.Is<TelemetryDataDto>(dto => dto.FuelLevelPercentage == null), 
            It.IsAny<CancellationToken>()), Times.Once);

        _mockSignalRService.Verify(s => s.SendVehicleStatusUpdateAsync(
            vehicleId,
            "Moving",
            timestamp,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNullMileage_ShouldHandleGracefully()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(45);
        var fuelLevel = new FuelLevel(60);
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, fuelLevel, null, timestamp);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSignalRService.Verify(s => s.SendTelemetryUpdateAsync(
            vehicleId, 
            It.Is<TelemetryDataDto>(dto => dto.CurrentMileage == 0), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldCreateCorrectTelemetryDto()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var fuelLevel = new FuelLevel(75);
        var mileage = 1500m;
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, fuelLevel, mileage, timestamp);

        TelemetryDataDto? capturedDto = null;
        _mockSignalRService.Setup(s => s.SendTelemetryUpdateAsync(
            It.IsAny<Guid>(), 
            It.IsAny<TelemetryDataDto>(), 
            It.IsAny<CancellationToken>()))
            .Callback<Guid, TelemetryDataDto, CancellationToken>((_, dto, _) => capturedDto = dto);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        capturedDto.Should().NotBeNull();
        capturedDto!.VehicleId.Should().Be(vehicleId);
        capturedDto.Location.Latitude.Should().Be(location.Latitude);
        capturedDto.Location.Longitude.Should().Be(location.Longitude);
        capturedDto.SpeedKmh.Should().Be(speed.KilometersPerHour);
        capturedDto.SpeedMph.Should().Be(speed.MilesPerHour);
        capturedDto.FuelLevelPercentage.Should().Be(fuelLevel.Percentage);
        capturedDto.CurrentMileage.Should().Be(mileage);
        capturedDto.IsMoving.Should().BeTrue(); // Speed > 0
        capturedDto.Timestamp.Should().Be(timestamp);
        capturedDto.AdditionalData.Should().ContainKey("eventId");
        capturedDto.AdditionalData.Should().ContainKey("eventTimestamp");
    }

    [Fact]
    public async Task Handle_WhenSignalRServiceThrows_ShouldNotRethrow()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, null, null, timestamp);

        _mockSignalRService.Setup(s => s.SendTelemetryUpdateAsync(
            It.IsAny<Guid>(), 
            It.IsAny<TelemetryDataDto>(), 
            It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("SignalR error"));

        // Act & Assert
        var act = async () => await _handler.Handle(domainEvent, CancellationToken.None);
        await act.Should().NotThrowAsync();

        // Should log the error
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error processing TelemetryDataReceivedEvent")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldLogDebugAndInfoMessages()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var timestamp = DateTime.UtcNow;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, null, null, timestamp);

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Processing TelemetryDataReceivedEvent")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Successfully processed TelemetryDataReceivedEvent")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldPassTokenToServices()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var timestamp = DateTime.UtcNow;
        var cancellationToken = new CancellationTokenSource().Token;

        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, null, null, timestamp);

        // Act
        await _handler.Handle(domainEvent, cancellationToken);

        // Assert
        _mockSignalRService.Verify(s => s.SendTelemetryUpdateAsync(
            It.IsAny<Guid>(), 
            It.IsAny<TelemetryDataDto>(), 
            cancellationToken), Times.Once);

        _mockSignalRService.Verify(s => s.SendVehicleStatusUpdateAsync(
            It.IsAny<Guid>(),
            It.IsAny<string>(),
            It.IsAny<DateTime>(),
            cancellationToken), Times.Once);
    }
}
