using FleetXQ.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

/// <summary>
/// Implementation of password validation service
/// </summary>
public sealed class PasswordValidationService : IPasswordValidationService
{
    private readonly ILogger<PasswordValidationService> _logger;
    private readonly int _minLength;
    private readonly int _maxLength;
    private readonly bool _requireUppercase;
    private readonly bool _requireLowercase;
    private readonly bool _requireDigit;
    private readonly bool _requireSpecialChar;
    private readonly int _minUniqueChars;

    /// <summary>
    /// Initializes a new instance of the <see cref="PasswordValidationService"/> class
    /// </summary>
    /// <param name="configuration">The configuration</param>
    /// <param name="logger">The logger</param>
    public PasswordValidationService(IConfiguration configuration, ILogger<PasswordValidationService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load password policy from configuration with defaults
        _minLength = configuration.GetValue<int>("PasswordPolicy:MinLength", 8);
        _maxLength = configuration.GetValue<int>("PasswordPolicy:MaxLength", 128);
        _requireUppercase = configuration.GetValue<bool>("PasswordPolicy:RequireUppercase", true);
        _requireLowercase = configuration.GetValue<bool>("PasswordPolicy:RequireLowercase", true);
        _requireDigit = configuration.GetValue<bool>("PasswordPolicy:RequireDigit", true);
        _requireSpecialChar = configuration.GetValue<bool>("PasswordPolicy:RequireSpecialChar", true);
        _minUniqueChars = configuration.GetValue<int>("PasswordPolicy:MinUniqueChars", 4);

        _logger.LogInformation("Password validation service initialized with policy: MinLength={MinLength}, RequireUppercase={RequireUppercase}, RequireLowercase={RequireLowercase}, RequireDigit={RequireDigit}, RequireSpecialChar={RequireSpecialChar}",
            _minLength, _requireUppercase, _requireLowercase, _requireDigit, _requireSpecialChar);
    }

    /// <summary>
    /// Validates a password against security requirements
    /// </summary>
    /// <param name="password">The password to validate</param>
    /// <returns>The validation result</returns>
    public PasswordValidationResult ValidatePassword(string password)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(password))
        {
            errors.Add("Password is required");
            return PasswordValidationResult.Failed(errors.ToArray());
        }

        // Check length requirements
        if (password.Length < _minLength)
        {
            errors.Add($"Password must be at least {_minLength} characters long");
        }

        if (password.Length > _maxLength)
        {
            errors.Add($"Password cannot exceed {_maxLength} characters");
        }

        // Check character requirements
        if (_requireUppercase && !password.Any(char.IsUpper))
        {
            errors.Add("Password must contain at least one uppercase letter");
        }

        if (_requireLowercase && !password.Any(char.IsLower))
        {
            errors.Add("Password must contain at least one lowercase letter");
        }

        if (_requireDigit && !password.Any(char.IsDigit))
        {
            errors.Add("Password must contain at least one digit");
        }

        if (_requireSpecialChar && !password.Any(IsSpecialCharacter))
        {
            errors.Add("Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)");
        }

        // Check unique character requirement
        var uniqueChars = password.Distinct().Count();
        if (uniqueChars < _minUniqueChars)
        {
            errors.Add($"Password must contain at least {_minUniqueChars} unique characters");
        }

        // Check for common weak patterns
        if (ContainsCommonWeakPatterns(password))
        {
            errors.Add("Password contains common weak patterns and is not allowed");
        }

        // Check for sequential characters
        if (ContainsSequentialCharacters(password))
        {
            errors.Add("Password cannot contain sequential characters (e.g., 123, abc)");
        }

        if (errors.Any())
        {
            _logger.LogDebug("Password validation failed with {ErrorCount} errors", errors.Count);
            return PasswordValidationResult.Failed(errors.ToArray());
        }

        _logger.LogDebug("Password validation successful");
        return PasswordValidationResult.Success();
    }

    /// <summary>
    /// Gets the password requirements description
    /// </summary>
    /// <returns>The password requirements</returns>
    public string GetPasswordRequirements()
    {
        var requirements = new List<string>
        {
            $"Be between {_minLength} and {_maxLength} characters long"
        };

        if (_requireUppercase)
            requirements.Add("Contain at least one uppercase letter");

        if (_requireLowercase)
            requirements.Add("Contain at least one lowercase letter");

        if (_requireDigit)
            requirements.Add("Contain at least one digit");

        if (_requireSpecialChar)
            requirements.Add("Contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)");

        if (_minUniqueChars > 1)
            requirements.Add($"Contain at least {_minUniqueChars} unique characters");

        requirements.Add("Not contain common weak patterns or sequential characters");

        return "Password must:\n• " + string.Join("\n• ", requirements);
    }

    /// <summary>
    /// Checks if a character is a special character
    /// </summary>
    /// <param name="c">The character to check</param>
    /// <returns>True if the character is special</returns>
    private static bool IsSpecialCharacter(char c)
    {
        return "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c);
    }

    /// <summary>
    /// Checks if the password contains common weak patterns
    /// </summary>
    /// <param name="password">The password to check</param>
    /// <returns>True if weak patterns are found</returns>
    private static bool ContainsCommonWeakPatterns(string password)
    {
        var lowerPassword = password.ToLowerInvariant();

        // Common weak patterns
        var weakPatterns = new[]
        {
            "password", "123456", "qwerty", "admin", "user", "login",
            "welcome", "default", "guest", "test", "demo", "temp"
        };

        return weakPatterns.Any(pattern => lowerPassword.Contains(pattern));
    }

    /// <summary>
    /// Checks if the password contains sequential characters
    /// </summary>
    /// <param name="password">The password to check</param>
    /// <returns>True if sequential characters are found</returns>
    private static bool ContainsSequentialCharacters(string password)
    {
        for (int i = 0; i < password.Length - 2; i++)
        {
            var char1 = password[i];
            var char2 = password[i + 1];
            var char3 = password[i + 2];

            // Check for ascending sequence
            if (char2 == char1 + 1 && char3 == char2 + 1)
                return true;

            // Check for descending sequence
            if (char2 == char1 - 1 && char3 == char2 - 1)
                return true;
        }

        return false;
    }
}
