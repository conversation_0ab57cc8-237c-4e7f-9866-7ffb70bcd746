using FleetXQ.Api.Models;
using FleetXQ.Infrastructure.Data;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Xunit;

namespace FleetXQ.Api.Tests.Integration;

/// <summary>
/// Base class for API integration tests
/// </summary>
public abstract class ApiIntegrationTestBase : IClassFixture<IntegrationTestWebApplicationFactory>, IAsyncDisposable
{
    protected readonly IntegrationTestWebApplicationFactory Factory;
    protected readonly HttpClient HttpClient;
    protected readonly JsonSerializerOptions JsonOptions;

    protected ApiIntegrationTestBase(IntegrationTestWebApplicationFactory factory)
    {
        Factory = factory;
        HttpClient = Factory.CreateClient();
        
        JsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
    }

    /// <summary>
    /// Creates a test JWT token for authentication
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="username">The username</param>
    /// <param name="email">The email</param>
    /// <param name="roles">The user roles</param>
    /// <param name="isActive">Whether the user is active</param>
    /// <param name="expiryMinutes">Token expiry in minutes</param>
    /// <returns>The JWT token</returns>
    protected string CreateTestJwtToken(
        Guid userId, 
        string username = "testuser", 
        string email = "<EMAIL>",
        string[] roles = null!,
        bool isActive = true,
        int expiryMinutes = 60)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("YourSuperSecretKeyThatIsAtLeast32CharactersLong!"));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId.ToString()),
            new(ClaimTypes.Name, username),
            new(ClaimTypes.Email, email),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new("IsActive", isActive.ToString())
        };

        // Add roles
        var userRoles = roles ?? new[] { "User" };
        claims.AddRange(userRoles.Select(role => new Claim(ClaimTypes.Role, role)));

        var token = new JwtSecurityToken(
            issuer: "FleetXQ",
            audience: "FleetXQ-Users",
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(expiryMinutes),
            signingCredentials: credentials);

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    /// <summary>
    /// Sets the authorization header with a JWT token
    /// </summary>
    /// <param name="token">The JWT token</param>
    protected void SetAuthorizationHeader(string token)
    {
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    /// <summary>
    /// Clears the authorization header
    /// </summary>
    protected void ClearAuthorizationHeader()
    {
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    /// <summary>
    /// Creates an authenticated HTTP client with the specified user context
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="username">The username</param>
    /// <param name="roles">The user roles</param>
    /// <param name="isActive">Whether the user is active</param>
    /// <returns>The authenticated HTTP client</returns>
    protected HttpClient CreateAuthenticatedClient(
        Guid userId, 
        string username = "testuser", 
        string[] roles = null!,
        bool isActive = true)
    {
        var token = CreateTestJwtToken(userId, username, $"{username}@test.com", roles, isActive);
        var client = Factory.CreateClient();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    /// <summary>
    /// Sends a GET request and deserializes the response
    /// </summary>
    /// <typeparam name="T">The response type</typeparam>
    /// <param name="requestUri">The request URI</param>
    /// <returns>The deserialized response</returns>
    protected async Task<T?> GetAsync<T>(string requestUri)
    {
        var response = await HttpClient.GetAsync(requestUri);
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(content, JsonOptions);
    }

    /// <summary>
    /// Sends a POST request with JSON content and deserializes the response
    /// </summary>
    /// <typeparam name="TRequest">The request type</typeparam>
    /// <typeparam name="TResponse">The response type</typeparam>
    /// <param name="requestUri">The request URI</param>
    /// <param name="request">The request object</param>
    /// <returns>The HTTP response and deserialized content</returns>
    protected async Task<(HttpResponseMessage Response, TResponse? Content)> PostAsync<TRequest, TResponse>(
        string requestUri, 
        TRequest request)
    {
        var json = JsonSerializer.Serialize(request, JsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await HttpClient.PostAsync(requestUri, content);
        var responseContent = await response.Content.ReadAsStringAsync();
        var deserializedContent = JsonSerializer.Deserialize<TResponse>(responseContent, JsonOptions);
        
        return (response, deserializedContent);
    }

    /// <summary>
    /// Sends a PUT request with JSON content and deserializes the response
    /// </summary>
    /// <typeparam name="TRequest">The request type</typeparam>
    /// <typeparam name="TResponse">The response type</typeparam>
    /// <param name="requestUri">The request URI</param>
    /// <param name="request">The request object</param>
    /// <returns>The HTTP response and deserialized content</returns>
    protected async Task<(HttpResponseMessage Response, TResponse? Content)> PutAsync<TRequest, TResponse>(
        string requestUri, 
        TRequest request)
    {
        var json = JsonSerializer.Serialize(request, JsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await HttpClient.PutAsync(requestUri, content);
        var responseContent = await response.Content.ReadAsStringAsync();
        var deserializedContent = JsonSerializer.Deserialize<TResponse>(responseContent, JsonOptions);
        
        return (response, deserializedContent);
    }

    /// <summary>
    /// Sends a DELETE request and deserializes the response
    /// </summary>
    /// <typeparam name="T">The response type</typeparam>
    /// <param name="requestUri">The request URI</param>
    /// <returns>The HTTP response and deserialized content</returns>
    protected async Task<(HttpResponseMessage Response, T? Content)> DeleteAsync<T>(string requestUri)
    {
        var response = await HttpClient.DeleteAsync(requestUri);
        var responseContent = await response.Content.ReadAsStringAsync();
        var deserializedContent = JsonSerializer.Deserialize<T>(responseContent, JsonOptions);
        
        return (response, deserializedContent);
    }

    /// <summary>
    /// Asserts that the API response is successful
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="response">The HTTP response</param>
    /// <param name="apiResponse">The API response</param>
    protected static void AssertSuccessResponse<T>(HttpResponseMessage response, ApiResponse<T>? apiResponse)
    {
        response.Should().NotBeNull();
        response.IsSuccessStatusCode.Should().BeTrue();
        
        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeTrue();
        apiResponse.StatusCode.Should().Be((int)response.StatusCode);
        apiResponse.Data.Should().NotBeNull();
    }

    /// <summary>
    /// Asserts that the API response is an error
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="response">The HTTP response</param>
    /// <param name="apiResponse">The API response</param>
    /// <param name="expectedStatusCode">The expected status code</param>
    protected static void AssertErrorResponse<T>(
        HttpResponseMessage response, 
        ApiResponse<T>? apiResponse, 
        System.Net.HttpStatusCode expectedStatusCode)
    {
        response.Should().NotBeNull();
        response.StatusCode.Should().Be(expectedStatusCode);
        
        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeFalse();
        apiResponse.StatusCode.Should().Be((int)expectedStatusCode);
        apiResponse.Message.Should().NotBeNullOrEmpty();
    }

    /// <summary>
    /// Gets the database context for direct database operations
    /// </summary>
    /// <returns>The database context</returns>
    protected ApplicationDbContext GetDbContext()
    {
        return Factory.CreateDbContext();
    }

    /// <summary>
    /// Seeds the database with test data
    /// </summary>
    /// <param name="seedAction">The action to seed the database</param>
    protected async Task SeedDatabaseAsync(Func<ApplicationDbContext, Task> seedAction)
    {
        await Factory.SeedDatabaseAsync(seedAction);
    }

    /// <summary>
    /// Clears all data from the database
    /// </summary>
    protected async Task ClearDatabaseAsync()
    {
        await Factory.ClearDatabaseAsync();
    }

    /// <summary>
    /// Resets the database to a clean state
    /// </summary>
    protected async Task ResetDatabaseAsync()
    {
        await Factory.ResetDatabaseAsync();
    }

    /// <summary>
    /// Disposes the test resources
    /// </summary>
    public virtual async ValueTask DisposeAsync()
    {
        HttpClient?.Dispose();
        await Factory.ClearDatabaseAsync();
        GC.SuppressFinalize(this);
    }
}
