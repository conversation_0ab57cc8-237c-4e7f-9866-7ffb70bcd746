using AutoMapper;
using FleetXQ.Application.Features.Alerts.DTOs;
using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Events;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Alerts.EventHandlers;

/// <summary>
/// Handler for AlertTriggeredEvent that sends real-time alert notifications via SignalR
/// </summary>
public class AlertTriggeredEventHandler : INotificationHandler<AlertTriggeredEvent>
{
    private readonly ISignalRNotificationService _signalRNotificationService;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<AlertTriggeredEventHandler> _logger;

    public AlertTriggeredEventHandler(
        ISignalRNotificationService signalRNotificationService,
        IVehicleRepository vehicleRepository,
        IDriverRepository driverRepository,
        IMapper mapper,
        ILogger<AlertTriggeredEventHandler> logger)
    {
        _signalRNotificationService = signalRNotificationService ?? throw new ArgumentNullException(nameof(signalRNotificationService));
        _vehicleRepository = vehicleRepository ?? throw new ArgumentNullException(nameof(vehicleRepository));
        _driverRepository = driverRepository ?? throw new ArgumentNullException(nameof(driverRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the AlertTriggeredEvent by sending real-time notifications to SignalR clients
    /// </summary>
    /// <param name="notification">The domain event</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task Handle(AlertTriggeredEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing AlertTriggeredEvent for alert {AlertId} (Type: {AlertType}, Severity: {Severity})", 
                notification.AlertId, notification.AlertType, notification.Severity);

            // Create alert DTO from domain event
            var alertDto = await CreateAlertDto(notification, cancellationToken);

            // Send alert notification to SignalR clients
            await _signalRNotificationService.SendAlertNotificationAsync(alertDto, cancellationToken);

            // Log based on severity
            if (notification.RequiresImmediateAttention)
            {
                _logger.LogWarning("Critical alert triggered: {AlertId} - {Message}", 
                    notification.AlertId, notification.Message);
            }
            else
            {
                _logger.LogInformation("Alert triggered: {AlertId} - {Message}", 
                    notification.AlertId, notification.Message);
            }

            _logger.LogInformation("Successfully processed AlertTriggeredEvent for alert {AlertId}", 
                notification.AlertId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing AlertTriggeredEvent for alert {AlertId}", 
                notification.AlertId);
            
            // Don't rethrow - we don't want to break the domain event processing pipeline
            // The alert has already been saved, this is just for real-time notifications
        }
    }

    /// <summary>
    /// Creates an AlertDto from the domain event
    /// </summary>
    private async Task<AlertDto> CreateAlertDto(AlertTriggeredEvent notification, CancellationToken cancellationToken)
    {
        string? vehicleName = null;
        string? driverName = null;

        // Get vehicle name if vehicle ID is provided
        if (notification.VehicleId.HasValue)
        {
            try
            {
                var vehicle = await _vehicleRepository.GetByIdAsync(notification.VehicleId.Value, cancellationToken);
                vehicleName = vehicle?.VehicleName;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to retrieve vehicle name for vehicle {VehicleId}", notification.VehicleId);
            }
        }

        // Get driver name if driver ID is provided
        if (notification.DriverId.HasValue)
        {
            try
            {
                var driver = await _driverRepository.GetByIdAsync(notification.DriverId.Value, cancellationToken);
                driverName = driver != null ? $"{driver.FirstName} {driver.LastName}".Trim() : null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to retrieve driver name for driver {DriverId}", notification.DriverId);
            }
        }

        return new AlertDto
        {
            Id = notification.AlertId,
            Type = notification.AlertType,
            Severity = notification.Severity,
            Status = Domain.Enums.AlertStatus.Active, // New alerts are always active
            Title = GenerateAlertTitle(notification),
            Description = notification.Message,
            VehicleId = notification.VehicleId,
            VehicleName = vehicleName,
            DriverId = notification.DriverId,
            DriverName = driverName,
            Location = notification.Location != null 
                ? new FleetXQ.Application.Features.Vehicles.DTOs.LocationDto
                {
                    Latitude = notification.Location.Latitude,
                    Longitude = notification.Location.Longitude
                }
                : null,
            TriggeredAt = notification.OccurredOn,
            AcknowledgedAt = null,
            AcknowledgedBy = null,
            ResolvedAt = null,
            ResolvedBy = null,
            AdditionalData = notification.Metadata.Any() 
                ? notification.Metadata.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
                : null,
            RequiresImmediateAttention = notification.RequiresImmediateAttention,
            CreatedAt = notification.OccurredOn,
            UpdatedAt = notification.OccurredOn
        };
    }

    /// <summary>
    /// Generates a user-friendly alert title based on the alert type and severity
    /// </summary>
    private static string GenerateAlertTitle(AlertTriggeredEvent notification)
    {
        var severityPrefix = notification.Severity switch
        {
            Domain.Enums.AlertSeverity.Critical => "🚨 CRITICAL",
            Domain.Enums.AlertSeverity.High => "⚠️ HIGH",
            Domain.Enums.AlertSeverity.Medium => "⚡ MEDIUM",
            Domain.Enums.AlertSeverity.Low => "ℹ️ LOW",
            _ => ""
        };

        var typeDescription = notification.AlertType switch
        {
            Domain.Enums.AlertType.Speed => "Speed Violation",
            Domain.Enums.AlertType.Fuel => "Fuel Alert",
            Domain.Enums.AlertType.Maintenance => "Maintenance Due",
            Domain.Enums.AlertType.HarshDriving => "Harsh Driving",
            Domain.Enums.AlertType.Geofence => "Geofence Violation",
            Domain.Enums.AlertType.System => "System Alert",
            _ => "Alert"
        };

        return $"{severityPrefix} {typeDescription}".Trim();
    }
}
