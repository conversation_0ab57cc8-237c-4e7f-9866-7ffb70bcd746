using FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;
using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;

namespace FleetXQ.Api.Hubs;

/// <summary>
/// SignalR hub for real-time dashboard updates and fleet monitoring
/// </summary>
[Authorize]
public class DashboardHub : BaseFleetXQHub
{
    private readonly IMediator _mediator;
    private readonly ISignalRConnectionManager _connectionManager;

    public DashboardHub(
        IMediator mediator,
        ISignalRConnectionManager connectionManager,
        SignalRConnectionStateService connectionStateService,
        ILogger<DashboardHub> logger) : base(logger, connectionStateService)
    {
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
    }

    /// <summary>
    /// Subscribe to real-time dashboard updates
    /// </summary>
    /// <param name="dashboardType">Type of dashboard (Fleet, Vehicle, Driver, Analytics)</param>
    /// <returns>Task representing the async operation</returns>
    public async Task SubscribeToDashboard(string dashboardType = "Fleet")
    {
        await ExecuteSafelyAsync(async () =>
        {
            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var userRoles = GetCurrentUserRoles();

            // Validate dashboard type and user permissions
            ValidateDashboardAccess(dashboardType, userRoles);

            var groupName = $"Dashboard_{dashboardType}";

            // Add connection to dashboard group
            await Groups.AddToGroupAsync(connectionId, groupName);
            
            // Track the subscription in connection manager
            await _connectionManager.AddToGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} with roles [{Roles}] subscribed to {DashboardType} dashboard", 
                userId, string.Join(", ", userRoles), dashboardType);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("DashboardSubscriptionConfirmed", new
            {
                DashboardType = dashboardType,
                Message = $"Successfully subscribed to {dashboardType} dashboard updates"
            });

            // Send initial dashboard data
            await SendInitialDashboardData(dashboardType);

        }, nameof(SubscribeToDashboard));
    }

    /// <summary>
    /// Unsubscribe from dashboard updates
    /// </summary>
    /// <param name="dashboardType">Type of dashboard to unsubscribe from</param>
    /// <returns>Task representing the async operation</returns>
    public async Task UnsubscribeFromDashboard(string dashboardType = "Fleet")
    {
        await ExecuteSafelyAsync(async () =>
        {
            var userId = GetCurrentUserId();
            var connectionId = GetConnectionId();
            var groupName = $"Dashboard_{dashboardType}";

            // Remove connection from dashboard group
            await Groups.RemoveFromGroupAsync(connectionId, groupName);
            
            // Remove the subscription from connection manager
            await _connectionManager.RemoveFromGroupAsync(userId, groupName);

            Logger.LogInformation("User {UserId} unsubscribed from {DashboardType} dashboard", 
                userId, dashboardType);

            // Send confirmation to the caller
            await Clients.Caller.SendAsync("DashboardUnsubscriptionConfirmed", new
            {
                DashboardType = dashboardType,
                Message = $"Successfully unsubscribed from {dashboardType} dashboard updates"
            });

        }, nameof(UnsubscribeFromDashboard));
    }

    /// <summary>
    /// Request current fleet metrics
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    public async Task RequestFleetMetrics()
    {
        await ExecuteSafelyAsync(async () =>
        {
            var userId = GetCurrentUserId();
            var userRoles = GetCurrentUserRoles();

            // Check if user has permission to view fleet metrics
            if (!HasRole("Admin") && !HasRole("Manager"))
            {
                throw new UnauthorizedAccessException("Insufficient permissions to view fleet metrics");
            }

            Logger.LogDebug("User {UserId} requested fleet metrics", userId);

            // Get latest telemetry for all vehicles
            var query = new GetLatestTelemetryQuery
            {
                ActiveVehiclesOnly = true,
                MaxAgeHours = 24
            };

            var result = await _mediator.Send(query);

            if (result.Success)
            {
                var metrics = CalculateFleetMetrics(result.TelemetryList);
                
                await Clients.Caller.SendAsync("FleetMetrics", metrics);
                
                Logger.LogInformation("Successfully sent fleet metrics to user {UserId}", userId);
            }
            else
            {
                await Clients.Caller.SendAsync("FleetMetrics", new
                {
                    Error = result.ErrorMessage ?? "Failed to retrieve fleet metrics",
                    Timestamp = DateTime.UtcNow
                });
            }

        }, nameof(RequestFleetMetrics));
    }

    /// <summary>
    /// Request vehicle status summary
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    public async Task RequestVehicleStatusSummary()
    {
        await ExecuteSafelyAsync(async () =>
        {
            var userId = GetCurrentUserId();
            
            Logger.LogDebug("User {UserId} requested vehicle status summary", userId);

            // Get latest telemetry for all vehicles
            var query = new GetLatestTelemetryQuery
            {
                ActiveVehiclesOnly = false,
                MaxAgeHours = 24
            };

            var result = await _mediator.Send(query);

            if (result.Success)
            {
                var summary = CalculateVehicleStatusSummary(result.TelemetryList);
                
                await Clients.Caller.SendAsync("VehicleStatusSummary", summary);
                
                Logger.LogInformation("Successfully sent vehicle status summary to user {UserId}", userId);
            }
            else
            {
                await Clients.Caller.SendAsync("VehicleStatusSummary", new
                {
                    Error = result.ErrorMessage ?? "Failed to retrieve vehicle status summary",
                    Timestamp = DateTime.UtcNow
                });
            }

        }, nameof(RequestVehicleStatusSummary));
    }

    /// <summary>
    /// Called when a connection is established
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        await base.OnConnectedAsync();
        
        var userId = GetCurrentUserId();
        var connectionId = GetConnectionId();
        
        // Register the connection
        await _connectionManager.AddConnectionAsync(userId, connectionId);
        
        Logger.LogInformation("User {UserId} connected to DashboardHub with connection {ConnectionId}", 
            userId, connectionId);
    }

    /// <summary>
    /// Called when a connection is terminated
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetCurrentUserId();
        var connectionId = GetConnectionId();
        
        // Unregister the connection and clean up group memberships
        await _connectionManager.RemoveConnectionAsync(userId, connectionId);
        
        // Get all groups the user was subscribed to and clean them up
        var userGroups = await _connectionManager.GetUserGroupsAsync(userId);
        foreach (var groupName in userGroups)
        {
            await _connectionManager.RemoveFromGroupAsync(userId, groupName);
        }
        
        Logger.LogInformation("User {UserId} disconnected from DashboardHub, cleaned up {GroupCount} group subscriptions", 
            userId, userGroups.Count());
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Validates dashboard access based on user roles
    /// </summary>
    private static void ValidateDashboardAccess(string dashboardType, IEnumerable<string> userRoles)
    {
        var roles = userRoles.ToList();
        
        switch (dashboardType.ToLowerInvariant())
        {
            case "fleet":
            case "analytics":
                if (!roles.Contains("Admin", StringComparer.OrdinalIgnoreCase) && 
                    !roles.Contains("Manager", StringComparer.OrdinalIgnoreCase))
                {
                    throw new UnauthorizedAccessException($"Insufficient permissions to access {dashboardType} dashboard");
                }
                break;
            case "vehicle":
            case "driver":
                // All authenticated users can access vehicle and driver dashboards
                break;
            default:
                throw new ArgumentException($"Unknown dashboard type: {dashboardType}");
        }
    }

    /// <summary>
    /// Sends initial dashboard data to a new subscriber
    /// </summary>
    private async Task SendInitialDashboardData(string dashboardType)
    {
        try
        {
            switch (dashboardType.ToLowerInvariant())
            {
                case "fleet":
                    await RequestFleetMetrics();
                    break;
                case "vehicle":
                    await RequestVehicleStatusSummary();
                    break;
                default:
                    // For other dashboard types, send a basic welcome message
                    await Clients.Caller.SendAsync("DashboardWelcome", new
                    {
                        DashboardType = dashboardType,
                        Message = $"Welcome to {dashboardType} dashboard",
                        Timestamp = DateTime.UtcNow
                    });
                    break;
            }
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to send initial dashboard data for {DashboardType}", dashboardType);
        }
    }

    /// <summary>
    /// Calculates fleet metrics from telemetry data
    /// </summary>
    private static object CalculateFleetMetrics(IEnumerable<FleetXQ.Application.Features.Telemetry.DTOs.TelemetryDto> telemetryList)
    {
        var telemetryArray = telemetryList.ToArray();
        var now = DateTime.UtcNow;
        
        return new
        {
            TotalVehicles = telemetryArray.Length,
            ActiveVehicles = telemetryArray.Count(t => now.Subtract(t.Timestamp).TotalMinutes < 30),
            AverageSpeed = telemetryArray.Any() ? telemetryArray.Average(t => t.SpeedKmh) : 0,
            AverageFuelLevel = telemetryArray.Where(t => t.FuelLevelPercentage.HasValue)
                                           .Select(t => t.FuelLevelPercentage!.Value)
                                           .DefaultIfEmpty(0)
                                           .Average(),
            VehiclesMoving = telemetryArray.Count(t => t.IsMoving),
            LastUpdated = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Calculates vehicle status summary from telemetry data
    /// </summary>
    private static object CalculateVehicleStatusSummary(IEnumerable<FleetXQ.Application.Features.Telemetry.DTOs.TelemetryDto> telemetryList)
    {
        var telemetryArray = telemetryList.ToArray();
        var now = DateTime.UtcNow;
        
        return new
        {
            Online = telemetryArray.Count(t => now.Subtract(t.Timestamp).TotalMinutes < 30),
            Offline = telemetryArray.Count(t => now.Subtract(t.Timestamp).TotalMinutes >= 30),
            Moving = telemetryArray.Count(t => t.IsMoving),
            Idle = telemetryArray.Count(t => !t.IsMoving && now.Subtract(t.Timestamp).TotalMinutes < 30),
            LowFuel = telemetryArray.Count(t => t.FuelLevelPercentage.HasValue && t.FuelLevelPercentage < 20),
            LastUpdated = DateTime.UtcNow
        };
    }
}
