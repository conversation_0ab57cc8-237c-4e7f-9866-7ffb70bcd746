using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Services;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;

namespace FleetXQ.Api.Tests.Hubs;

/// <summary>
/// Base class for SignalR hub unit tests providing common setup and utilities
/// </summary>
public abstract class SignalRHubTestBase
{
    protected readonly Mock<ISignalRConnectionManager> MockConnectionManager;
    protected readonly Mock<SignalRConnectionStateService> MockConnectionStateService;
    protected readonly Mock<IHubCallerClients> MockClients;
    protected readonly Mock<IClientProxy> MockCaller;
    protected readonly Mock<IClientProxy> MockAll;
    protected readonly Mock<IGroupManager> MockGroups;
    protected readonly Mock<HubCallerContext> MockContext;
    protected readonly Mock<ILogger> MockLogger;
    
    protected readonly Guid TestUserId = Guid.NewGuid();
    protected readonly string TestConnectionId = "test-connection-123";
    protected readonly string TestUserAgent = "Test User Agent";
    protected readonly string TestIpAddress = "127.0.0.1";

    protected SignalRHubTestBase()
    {
        // Setup SignalR mocks
        MockConnectionManager = new Mock<ISignalRConnectionManager>();
        MockConnectionStateService = new Mock<SignalRConnectionStateService>(
            MockConnectionManager.Object, 
            Mock.Of<ILogger<SignalRConnectionStateService>>());
        
        MockClients = new Mock<IHubCallerClients>();
        MockCaller = new Mock<IClientProxy>();
        MockAll = new Mock<IClientProxy>();
        MockGroups = new Mock<IGroupManager>();
        MockContext = new Mock<HubCallerContext>();
        MockLogger = new Mock<ILogger>();

        // Setup default behavior
        SetupDefaultMockBehavior();
    }

    /// <summary>
    /// Sets up default mock behavior for common SignalR operations
    /// </summary>
    private void SetupDefaultMockBehavior()
    {
        // Setup clients
        MockClients.Setup(c => c.Caller).Returns(MockCaller.Object);
        MockClients.Setup(c => c.All).Returns(MockAll.Object);
        MockClients.Setup(c => c.Group(It.IsAny<string>())).Returns(MockCaller.Object);

        // Setup context
        MockContext.Setup(c => c.ConnectionId).Returns(TestConnectionId);
        MockContext.Setup(c => c.User).Returns(CreateTestClaimsPrincipal());

        // Setup groups
        MockGroups.Setup(g => g.AddToGroupAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        MockGroups.Setup(g => g.RemoveFromGroupAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Setup connection manager
        MockConnectionManager.Setup(cm => cm.AddConnectionAsync(It.IsAny<Guid>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);
        MockConnectionManager.Setup(cm => cm.RemoveConnectionAsync(It.IsAny<Guid>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);
        MockConnectionManager.Setup(cm => cm.AddToGroupAsync(It.IsAny<Guid>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);
        MockConnectionManager.Setup(cm => cm.RemoveFromGroupAsync(It.IsAny<Guid>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);
        MockConnectionManager.Setup(cm => cm.GetConnectionsAsync(It.IsAny<Guid>()))
            .ReturnsAsync(new[] { TestConnectionId });
        MockConnectionManager.Setup(cm => cm.GetUserIdAsync(It.IsAny<string>()))
            .ReturnsAsync(TestUserId);
        MockConnectionManager.Setup(cm => cm.IsUserConnectedAsync(It.IsAny<Guid>()))
            .ReturnsAsync(true);

        // Setup client proxy SendAsync calls
        MockCaller.Setup(c => c.SendAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        MockAll.Setup(c => c.SendAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
    }

    /// <summary>
    /// Creates a test ClaimsPrincipal with the test user ID and default roles
    /// </summary>
    protected ClaimsPrincipal CreateTestClaimsPrincipal(params string[] roles)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, TestUserId.ToString()),
            new(ClaimTypes.Name, "Test User"),
            new(ClaimTypes.Email, "<EMAIL>")
        };

        // Add roles (default to "User" if none specified)
        var userRoles = roles.Any() ? roles : new[] { "User" };
        claims.AddRange(userRoles.Select(role => new Claim(ClaimTypes.Role, role)));

        var identity = new ClaimsIdentity(claims, "Test");
        return new ClaimsPrincipal(identity);
    }

    /// <summary>
    /// Sets up the hub context with the provided claims principal
    /// </summary>
    protected void SetupHubContext(ClaimsPrincipal? user = null)
    {
        MockContext.Setup(c => c.User).Returns(user ?? CreateTestClaimsPrincipal());
    }

    /// <summary>
    /// Verifies that a client method was called with the expected parameters
    /// </summary>
    protected void VerifyClientMethodCalled(string methodName, object? expectedData = null, Times? times = null)
    {
        if (expectedData != null)
        {
            MockCaller.Verify(
                c => c.SendAsync(methodName, expectedData, It.IsAny<CancellationToken>()),
                times ?? Times.Once);
        }
        else
        {
            MockCaller.Verify(
                c => c.SendAsync(methodName, It.IsAny<object>(), It.IsAny<CancellationToken>()),
                times ?? Times.Once);
        }
    }

    /// <summary>
    /// Verifies that a group method was called
    /// </summary>
    protected void VerifyGroupMethodCalled(string groupName, string methodName, object? expectedData = null, Times? times = null)
    {
        var mockGroupClient = new Mock<IClientProxy>();
        MockClients.Setup(c => c.Group(groupName)).Returns(mockGroupClient.Object);

        if (expectedData != null)
        {
            mockGroupClient.Verify(
                c => c.SendAsync(methodName, expectedData, It.IsAny<CancellationToken>()),
                times ?? Times.Once);
        }
        else
        {
            mockGroupClient.Verify(
                c => c.SendAsync(methodName, It.IsAny<object>(), It.IsAny<CancellationToken>()),
                times ?? Times.Once);
        }
    }

    /// <summary>
    /// Verifies that the user was added to a group
    /// </summary>
    protected void VerifyAddedToGroup(string groupName, Times? times = null)
    {
        MockGroups.Verify(
            g => g.AddToGroupAsync(TestConnectionId, groupName, It.IsAny<CancellationToken>()),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that the user was removed from a group
    /// </summary>
    protected void VerifyRemovedFromGroup(string groupName, Times? times = null)
    {
        MockGroups.Verify(
            g => g.RemoveFromGroupAsync(TestConnectionId, groupName, It.IsAny<CancellationToken>()),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that the connection manager was called to add a connection
    /// </summary>
    protected void VerifyConnectionAdded(Times? times = null)
    {
        MockConnectionManager.Verify(
            cm => cm.AddConnectionAsync(TestUserId, TestConnectionId),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that the connection manager was called to remove a connection
    /// </summary>
    protected void VerifyConnectionRemoved(Times? times = null)
    {
        MockConnectionManager.Verify(
            cm => cm.RemoveConnectionAsync(TestUserId, TestConnectionId),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that the connection manager was called to add user to group
    /// </summary>
    protected void VerifyUserAddedToGroup(string groupName, Times? times = null)
    {
        MockConnectionManager.Verify(
            cm => cm.AddToGroupAsync(TestUserId, groupName),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that the connection manager was called to remove user from group
    /// </summary>
    protected void VerifyUserRemovedFromGroup(string groupName, Times? times = null)
    {
        MockConnectionManager.Verify(
            cm => cm.RemoveFromGroupAsync(TestUserId, groupName),
            times ?? Times.Once);
    }

    /// <summary>
    /// Creates a cancellation token for testing
    /// </summary>
    protected static CancellationToken CreateCancellationToken()
    {
        return new CancellationTokenSource().Token;
    }

    /// <summary>
    /// Creates a mock logger for the specified type
    /// </summary>
    protected static Mock<ILogger<T>> CreateMockLogger<T>()
    {
        return new Mock<ILogger<T>>();
    }
}
