using FleetXQ.Api.Hubs;
using FleetXQ.Application.Features.Alerts.DTOs;
using FleetXQ.Application.Features.Telemetry.DTOs;
using FleetXQ.Application.Interfaces;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Infrastructure.Services;

/// <summary>
/// Service for sending SignalR notifications to connected clients
/// </summary>
public class SignalRNotificationService : ISignalRNotificationService
{
    private readonly IHubContext<TelemetryHub> _telemetryHubContext;
    private readonly IHubContext<AlertHub> _alertHubContext;
    private readonly IHubContext<DashboardHub> _dashboardHubContext;
    private readonly ILogger<SignalRNotificationService> _logger;

    public SignalRNotificationService(
        IHubContext<TelemetryHub> telemetryHubContext,
        IHubContext<AlertHub> alertHubContext,
        IHubContext<DashboardHub> dashboardHubContext,
        ILogger<SignalRNotificationService> logger)
    {
        _telemetryHubContext = telemetryHubContext ?? throw new ArgumentNullException(nameof(telemetryHubContext));
        _alertHubContext = alertHubContext ?? throw new ArgumentNullException(nameof(alertHubContext));
        _dashboardHubContext = dashboardHubContext ?? throw new ArgumentNullException(nameof(dashboardHubContext));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Sends telemetry update to vehicle subscribers
    /// </summary>
    public async Task SendTelemetryUpdateAsync(Guid vehicleId, TelemetryDataDto telemetryData, CancellationToken cancellationToken = default)
    {
        try
        {
            if (vehicleId == Guid.Empty)
                throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));

            if (telemetryData == null)
                throw new ArgumentNullException(nameof(telemetryData));

            var vehicleGroupName = $"Vehicle_{vehicleId}";

            // Send to vehicle-specific subscribers
            await _telemetryHubContext.Clients.Group(vehicleGroupName)
                .SendAsync("TelemetryUpdate", telemetryData, cancellationToken);

            // Send to dashboard subscribers
            await _dashboardHubContext.Clients.Group("Dashboard_Fleet")
                .SendAsync("VehicleStatusUpdate", new
                {
                    VehicleId = vehicleId,
                    Status = telemetryData.IsMoving ? "Moving" : "Idle",
                    LastUpdate = telemetryData.Timestamp,
                    Location = telemetryData.Location,
                    Speed = telemetryData.SpeedKmh,
                    FuelLevel = telemetryData.FuelLevelPercentage
                }, cancellationToken);

            _logger.LogDebug("Sent telemetry update for vehicle {VehicleId} to subscribers", vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send telemetry update for vehicle {VehicleId}", vehicleId);
            throw;
        }
    }

    /// <summary>
    /// Sends alert notification to alert subscribers
    /// </summary>
    public async Task SendAlertNotificationAsync(AlertDto alert, CancellationToken cancellationToken = default)
    {
        try
        {
            if (alert == null)
                throw new ArgumentNullException(nameof(alert));

            // Send to general alert subscribers
            var generalGroupName = $"Alerts_Severity_{alert.Severity}";
            await _alertHubContext.Clients.Group(generalGroupName)
                .SendAsync("AlertNotification", alert, cancellationToken);

            // Send to type-specific subscribers
            var typeGroupName = $"Alerts_Type_{alert.Type}";
            await _alertHubContext.Clients.Group(typeGroupName)
                .SendAsync("AlertNotification", alert, cancellationToken);

            // Send to vehicle-specific alert subscribers if applicable
            if (alert.VehicleId.HasValue)
            {
                var vehicleAlertGroupName = $"VehicleAlerts_{alert.VehicleId.Value}";
                await _alertHubContext.Clients.Group(vehicleAlertGroupName)
                    .SendAsync("VehicleAlert", alert, cancellationToken);
            }

            // Send to role-based groups
            await SendAlertToRoleBasedGroups(alert, cancellationToken);

            // Send to dashboard subscribers
            await _dashboardHubContext.Clients.Group("Dashboard_Fleet")
                .SendAsync("AlertUpdate", new
                {
                    AlertId = alert.Id,
                    Type = alert.Type.ToString(),
                    Severity = alert.Severity.ToString(),
                    VehicleId = alert.VehicleId,
                    VehicleName = alert.VehicleName,
                    Message = alert.Description,
                    TriggeredAt = alert.TriggeredAt,
                    RequiresImmediateAttention = alert.RequiresImmediateAttention
                }, cancellationToken);

            _logger.LogInformation("Sent alert notification for alert {AlertId} (Type: {AlertType}, Severity: {Severity})", 
                alert.Id, alert.Type, alert.Severity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send alert notification for alert {AlertId}", alert?.Id);
            throw;
        }
    }

    /// <summary>
    /// Sends vehicle status update to dashboard subscribers
    /// </summary>
    public async Task SendVehicleStatusUpdateAsync(Guid vehicleId, string status, DateTime lastUpdate, CancellationToken cancellationToken = default)
    {
        try
        {
            if (vehicleId == Guid.Empty)
                throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));

            if (string.IsNullOrWhiteSpace(status))
                throw new ArgumentException("Status cannot be empty", nameof(status));

            var statusUpdate = new
            {
                VehicleId = vehicleId,
                Status = status,
                LastUpdate = lastUpdate,
                Timestamp = DateTime.UtcNow
            };

            // Send to dashboard subscribers
            await _dashboardHubContext.Clients.Group("Dashboard_Fleet")
                .SendAsync("VehicleStatusUpdate", statusUpdate, cancellationToken);

            await _dashboardHubContext.Clients.Group("Dashboard_Vehicle")
                .SendAsync("VehicleStatusUpdate", statusUpdate, cancellationToken);

            _logger.LogDebug("Sent vehicle status update for vehicle {VehicleId}: {Status}", vehicleId, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send vehicle status update for vehicle {VehicleId}", vehicleId);
            throw;
        }
    }

    /// <summary>
    /// Sends dashboard metrics update
    /// </summary>
    public async Task SendDashboardMetricsUpdateAsync(object metrics, CancellationToken cancellationToken = default)
    {
        try
        {
            if (metrics == null)
                throw new ArgumentNullException(nameof(metrics));

            // Send to all dashboard subscribers
            await _dashboardHubContext.Clients.Group("Dashboard_Fleet")
                .SendAsync("MetricsUpdate", metrics, cancellationToken);

            await _dashboardHubContext.Clients.Group("Dashboard_Analytics")
                .SendAsync("MetricsUpdate", metrics, cancellationToken);

            _logger.LogDebug("Sent dashboard metrics update to subscribers");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send dashboard metrics update");
            throw;
        }
    }

    /// <summary>
    /// Sends notification to specific user
    /// </summary>
    public async Task SendToUserAsync(Guid userId, string method, object data, CancellationToken cancellationToken = default)
    {
        try
        {
            if (userId == Guid.Empty)
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            if (string.IsNullOrWhiteSpace(method))
                throw new ArgumentException("Method cannot be empty", nameof(method));

            // Note: This requires a user-to-connection mapping service
            // For now, we'll log that this functionality needs to be implemented
            _logger.LogWarning("SendToUserAsync not fully implemented - requires user-to-connection mapping");
            
            // TODO: Implement user-specific messaging using ISignalRConnectionManager
            // var connections = await _connectionManager.GetConnectionsAsync(userId);
            // foreach (var connectionId in connections)
            // {
            //     await _hubContext.Clients.Client(connectionId).SendAsync(method, data, cancellationToken);
            // }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send notification to user {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// Sends notification to specific group
    /// </summary>
    public async Task SendToGroupAsync(string groupName, string method, object data, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(groupName))
                throw new ArgumentException("Group name cannot be empty", nameof(groupName));

            if (string.IsNullOrWhiteSpace(method))
                throw new ArgumentException("Method cannot be empty", nameof(method));

            // Determine which hub context to use based on group name
            if (groupName.StartsWith("Vehicle_", StringComparison.OrdinalIgnoreCase))
            {
                await _telemetryHubContext.Clients.Group(groupName)
                    .SendAsync(method, data, cancellationToken);
            }
            else if (groupName.StartsWith("Alert", StringComparison.OrdinalIgnoreCase))
            {
                await _alertHubContext.Clients.Group(groupName)
                    .SendAsync(method, data, cancellationToken);
            }
            else if (groupName.StartsWith("Dashboard", StringComparison.OrdinalIgnoreCase))
            {
                await _dashboardHubContext.Clients.Group(groupName)
                    .SendAsync(method, data, cancellationToken);
            }
            else
            {
                // Default to all hubs for unknown group types
                await _telemetryHubContext.Clients.Group(groupName)
                    .SendAsync(method, data, cancellationToken);
                await _alertHubContext.Clients.Group(groupName)
                    .SendAsync(method, data, cancellationToken);
                await _dashboardHubContext.Clients.Group(groupName)
                    .SendAsync(method, data, cancellationToken);
            }

            _logger.LogDebug("Sent notification to group {GroupName} with method {Method}", groupName, method);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send notification to group {GroupName}", groupName);
            throw;
        }
    }

    /// <summary>
    /// Sends notification to all connected clients
    /// </summary>
    public async Task SendToAllAsync(string method, object data, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(method))
                throw new ArgumentException("Method cannot be empty", nameof(method));

            // Send to all hubs
            await _telemetryHubContext.Clients.All.SendAsync(method, data, cancellationToken);
            await _alertHubContext.Clients.All.SendAsync(method, data, cancellationToken);
            await _dashboardHubContext.Clients.All.SendAsync(method, data, cancellationToken);

            _logger.LogDebug("Sent notification to all clients with method {Method}", method);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send notification to all clients");
            throw;
        }
    }

    /// <summary>
    /// Sends alert to role-based groups
    /// </summary>
    private async Task SendAlertToRoleBasedGroups(AlertDto alert, CancellationToken cancellationToken)
    {
        try
        {
            // Send to admin group (all alerts)
            await _alertHubContext.Clients.Group("Alerts_Admin")
                .SendAsync("AlertNotification", alert, cancellationToken);

            // Send to manager group (high and critical alerts)
            if (alert.Severity == Domain.Enums.AlertSeverity.High || alert.Severity == Domain.Enums.AlertSeverity.Critical)
            {
                await _alertHubContext.Clients.Group("Alerts_Manager")
                    .SendAsync("AlertNotification", alert, cancellationToken);
            }

            // Send to driver group (only critical alerts related to their vehicles)
            if (alert.Severity == Domain.Enums.AlertSeverity.Critical && alert.VehicleId.HasValue)
            {
                await _alertHubContext.Clients.Group("Alerts_Driver")
                    .SendAsync("AlertNotification", alert, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send alert to some role-based groups for alert {AlertId}", alert.Id);
        }
    }
}
