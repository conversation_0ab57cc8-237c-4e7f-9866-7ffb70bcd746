using FleetXQ.Domain.Enums;
using MediatR;

namespace FleetXQ.Application.Features.Drivers.Commands.UpdateDriverStatus;

/// <summary>
/// Command to update driver status
/// </summary>
public sealed class UpdateDriverStatusCommand : IRequest<UpdateDriverStatusResult>
{
    /// <summary>
    /// Gets or sets the driver ID
    /// </summary>
    public Guid DriverId { get; set; }

    /// <summary>
    /// Gets or sets the new status
    /// </summary>
    public DriverStatus Status { get; set; }
}

/// <summary>
/// Result of updating driver status
/// </summary>
public sealed class UpdateDriverStatusResult
{
    /// <summary>
    /// Gets or sets the updated driver
    /// </summary>
    public FleetXQ.Application.Features.Drivers.DTOs.DriverDto? Driver { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="driver">The updated driver</param>
    /// <returns>A successful result</returns>
    public static UpdateDriverStatusResult Successful(FleetXQ.Application.Features.Drivers.DTOs.DriverDto driver)
    {
        return new UpdateDriverStatusResult
        {
            Driver = driver,
            Success = true
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static UpdateDriverStatusResult Failed(string errorMessage)
    {
        return new UpdateDriverStatusResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
