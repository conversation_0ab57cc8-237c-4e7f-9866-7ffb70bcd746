using FleetXQ.Application.Interfaces;
using System.Security.Claims;

namespace FleetXQ.Api.Middleware;

/// <summary>
/// Middleware for injecting user context into the request pipeline
/// </summary>
public sealed class UserContextMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UserContextMiddleware> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="UserContextMiddleware"/> class
    /// </summary>
    /// <param name="next">The next middleware in the pipeline</param>
    /// <param name="logger">The logger</param>
    public UserContextMiddleware(RequestDelegate next, ILogger<UserContextMiddleware> logger)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Invokes the middleware
    /// </summary>
    /// <param name="context">The HTTP context</param>
    /// <param name="authorizationService">The authorization service</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task InvokeAsync(HttpContext context, IAuthorizationService authorizationService)
    {
        try
        {
            // Skip user context injection for non-authenticated requests
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                var userContext = authorizationService.ValidateUserContext(context.User);
                if (userContext != null)
                {
                    // Add user context to HTTP context items for easy access
                    context.Items["UserContext"] = userContext;
                    
                    _logger.LogDebug("User context injected for user {UserId} with role {Role}", 
                        userContext.UserId, userContext.Role);
                }
                else
                {
                    _logger.LogWarning("Failed to create user context for authenticated user");
                }
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UserContextMiddleware");
            await _next(context);
        }
    }
}

/// <summary>
/// Extension methods for UserContextMiddleware
/// </summary>
public static class UserContextMiddlewareExtensions
{
    /// <summary>
    /// Adds the user context middleware to the pipeline
    /// </summary>
    /// <param name="builder">The application builder</param>
    /// <returns>The application builder</returns>
    public static IApplicationBuilder UseUserContext(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<UserContextMiddleware>();
    }
}

/// <summary>
/// Service for accessing user context in controllers and services
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Gets the current user context
    /// </summary>
    UserContext? CurrentUser { get; }

    /// <summary>
    /// Gets the current user ID
    /// </summary>
    Guid? UserId { get; }

    /// <summary>
    /// Gets the current username
    /// </summary>
    string? Username { get; }

    /// <summary>
    /// Gets the current user role
    /// </summary>
    string? Role { get; }

    /// <summary>
    /// Checks if the current user is authenticated
    /// </summary>
    bool IsAuthenticated { get; }

    /// <summary>
    /// Checks if the current user has a specific permission
    /// </summary>
    /// <param name="permission">The permission to check</param>
    /// <returns>True if the user has the permission</returns>
    bool HasPermission(string permission);
}

/// <summary>
/// Implementation of current user service
/// </summary>
public sealed class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<CurrentUserService> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="CurrentUserService"/> class
    /// </summary>
    /// <param name="httpContextAccessor">The HTTP context accessor</param>
    /// <param name="logger">The logger</param>
    public CurrentUserService(IHttpContextAccessor httpContextAccessor, ILogger<CurrentUserService> logger)
    {
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets the current user context
    /// </summary>
    public UserContext? CurrentUser
    {
        get
        {
            var context = _httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("UserContext", out var userContextObj) == true)
            {
                return userContextObj as UserContext;
            }

            return null;
        }
    }

    /// <summary>
    /// Gets the current user ID
    /// </summary>
    public Guid? UserId => CurrentUser?.UserId;

    /// <summary>
    /// Gets the current username
    /// </summary>
    public string? Username => CurrentUser?.Username;

    /// <summary>
    /// Gets the current user role
    /// </summary>
    public string? Role => CurrentUser?.Role.ToString();

    /// <summary>
    /// Checks if the current user is authenticated
    /// </summary>
    public bool IsAuthenticated
    {
        get
        {
            var context = _httpContextAccessor.HttpContext;
            return context?.User?.Identity?.IsAuthenticated == true && CurrentUser != null;
        }
    }

    /// <summary>
    /// Checks if the current user has a specific permission
    /// </summary>
    /// <param name="permission">The permission to check</param>
    /// <returns>True if the user has the permission</returns>
    public bool HasPermission(string permission)
    {
        if (string.IsNullOrWhiteSpace(permission))
            return false;

        var userContext = CurrentUser;
        if (userContext == null)
        {
            _logger.LogDebug("Permission check failed - no user context available");
            return false;
        }

        var hasPermission = userContext.Permissions.Contains(permission);
        _logger.LogDebug("Permission check for {Permission}: {HasPermission} (User: {UserId})", 
            permission, hasPermission, userContext.UserId);

        return hasPermission;
    }
}
