using FleetXQ.Application;
using FleetXQ.Infrastructure;
using FleetXQ.Api.Extensions;
using FleetXQ.Api.Middleware;
using FleetXQ.Api.Hubs;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Enrichers.AspNetCore;
using Microsoft.ApplicationInsights.Extensibility;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog with enhanced enrichers
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .Enrich.WithMachineName()
    .Enrich.WithThreadId()
    .Enrich.WithProcessId()
    .Enrich.WithProcessName()
    .Enrich.WithEnvironmentName()
    .Enrich.WithEnvironmentUserName()
    .Enrich.WithCorrelationId()
    .Enrich.WithClientIp()
    .Enrich.WithRequestId()
    .Enrich.WithRequestPath()
    .Enrich.WithUserClaims()
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

// Add Application Insights
builder.Services.AddApplicationInsightsTelemetry(builder.Configuration);

// Add Application and Infrastructure layers
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

// Add Authentication and Authorization
builder.Services.AddJwtAuthentication(builder.Configuration);
builder.Services.AddAuthorizationPolicies();

// Add CORS
builder.Services.AddCorsConfiguration(builder.Configuration);

// Add Swagger/OpenAPI
builder.Services.AddSwaggerWithJwt();

// Add SignalR with configuration
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
    options.HandshakeTimeout = TimeSpan.FromSeconds(15);
    options.MaximumReceiveMessageSize = 32 * 1024; // 32KB
})
.AddHubOptions<TelemetryHub>(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
})
.AddHubOptions<AlertHub>(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
})
.AddHubOptions<DashboardHub>(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
});

// Add SignalR error filter
builder.Services.AddSingleton<SignalRErrorFilter>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "FleetXQ API V1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

// Add global exception handling middleware
app.UseMiddleware<GlobalExceptionHandlingMiddleware>();

// Add comprehensive request/response logging middleware
app.UseMiddleware<RequestResponseLoggingMiddleware>();

// Add SignalR error handling middleware
app.UseMiddleware<SignalRErrorHandlingMiddleware>();

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.UseAuthentication();
app.UseUserContext(); // Add user context middleware after authentication
app.UseAuthorization();

app.MapControllers();

// Map SignalR hubs
app.MapHub<TelemetryHub>("/hubs/telemetry");
app.MapHub<AlertHub>("/hubs/alerts");
app.MapHub<DashboardHub>("/hubs/dashboard");

try
{
    Log.Information("Starting FleetXQ API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
