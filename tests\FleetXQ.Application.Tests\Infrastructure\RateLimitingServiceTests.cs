using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Authentication;
using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace FleetXQ.Application.Tests.Infrastructure;

/// <summary>
/// Unit tests for RateLimitingService
/// </summary>
public sealed class RateLimitingServiceTests : IDisposable
{
    private readonly IMemoryCache _memoryCache;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<RateLimitingService>> _mockLogger;
    private readonly RateLimitingService _rateLimitingService;

    public RateLimitingServiceTests()
    {
        _memoryCache = new MemoryCache(new MemoryCacheOptions());
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<RateLimitingService>>();

        // Setup default configuration
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:Login:MaxRequests", 5)).Returns(5);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:Login:TimeWindowMinutes", 15)).Returns(15);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:Login:LockoutDurationMinutes", 30)).Returns(30);

        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:Refresh:MaxRequests", 10)).Returns(10);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:Refresh:TimeWindowMinutes", 5)).Returns(5);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:Refresh:LockoutDurationMinutes", 15)).Returns(15);

        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:PasswordReset:MaxRequests", 3)).Returns(3);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:PasswordReset:TimeWindowHours", 1)).Returns(1);
        _mockConfiguration.Setup(x => x.GetValue<int>("RateLimit:PasswordReset:LockoutDurationHours", 2)).Returns(2);

        _rateLimitingService = new RateLimitingService(_memoryCache, _mockConfiguration.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task IsRequestAllowedAsync_FirstRequest_ReturnsTrue()
    {
        // Arrange
        var key = "***********";
        var endpoint = "login";

        // Act
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, endpoint);

        // Assert
        isAllowed.Should().BeTrue();
    }

    [Fact]
    public async Task IsRequestAllowedAsync_WithinLimit_ReturnsTrue()
    {
        // Arrange
        var key = "***********";
        var endpoint = "login";

        // Record 3 failed attempts (within limit of 5)
        for (int i = 0; i < 3; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key, endpoint);
        }

        // Act
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, endpoint);

        // Assert
        isAllowed.Should().BeTrue();
    }

    [Fact]
    public async Task IsRequestAllowedAsync_ExceedsLimit_ReturnsFalse()
    {
        // Arrange
        var key = "***********";
        var endpoint = "login";

        // Record 5 failed attempts (at the limit)
        for (int i = 0; i < 5; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key, endpoint);
        }

        // Act
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, endpoint);

        // Assert
        isAllowed.Should().BeFalse();
    }

    [Fact]
    public async Task RecordFailedAttemptAsync_MultipleAttempts_IncrementsCount()
    {
        // Arrange
        var key = "***********";
        var endpoint = "login";

        // Act
        await _rateLimitingService.RecordFailedAttemptAsync(key, endpoint);
        await _rateLimitingService.RecordFailedAttemptAsync(key, endpoint);

        // Check if still allowed (should be true for 2 attempts out of 5)
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, endpoint);

        // Assert
        isAllowed.Should().BeTrue();
    }

    [Fact]
    public async Task RecordSuccessfulAttemptAsync_ClearsRateLimit()
    {
        // Arrange
        var key = "***********";
        var endpoint = "login";

        // Record some failed attempts
        for (int i = 0; i < 3; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key, endpoint);
        }

        // Act
        await _rateLimitingService.RecordSuccessfulAttemptAsync(key, endpoint);

        // Check if allowed again (should be true since record was cleared)
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, endpoint);

        // Assert
        isAllowed.Should().BeTrue();
    }

    [Fact]
    public async Task GetRemainingLockoutTimeAsync_NoLockout_ReturnsNull()
    {
        // Arrange
        var key = "***********";
        var endpoint = "login";

        // Act
        var remainingTime = await _rateLimitingService.GetRemainingLockoutTimeAsync(key, endpoint);

        // Assert
        remainingTime.Should().BeNull();
    }

    [Fact]
    public async Task GetRemainingLockoutTimeAsync_ActiveLockout_ReturnsRemainingTime()
    {
        // Arrange
        var key = "***********";
        var endpoint = "login";

        // Exceed the limit to trigger lockout
        for (int i = 0; i < 5; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key, endpoint);
        }

        // Act
        var remainingTime = await _rateLimitingService.GetRemainingLockoutTimeAsync(key, endpoint);

        // Assert
        remainingTime.Should().NotBeNull();
        remainingTime.Should().BeGreaterThan(TimeSpan.Zero);
        remainingTime.Should().BeLessOrEqualTo(TimeSpan.FromMinutes(30)); // Max lockout duration
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsRequestAllowedAsync_EmptyKey_ReturnsTrue(string? key)
    {
        // Act
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key!, "login");

        // Assert
        isAllowed.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsRequestAllowedAsync_EmptyEndpoint_ReturnsTrue(string? endpoint)
    {
        // Act
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync("***********", endpoint!);

        // Assert
        isAllowed.Should().BeTrue();
    }

    [Fact]
    public async Task IsRequestAllowedAsync_DifferentEndpoints_IndependentLimits()
    {
        // Arrange
        var key = "***********";

        // Exceed limit for login endpoint
        for (int i = 0; i < 5; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key, "login");
        }

        // Act
        var loginAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, "login");
        var refreshAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, "refresh");

        // Assert
        loginAllowed.Should().BeFalse();
        refreshAllowed.Should().BeTrue(); // Different endpoint, should be allowed
    }

    [Fact]
    public async Task IsRequestAllowedAsync_DifferentKeys_IndependentLimits()
    {
        // Arrange
        var key1 = "***********";
        var key2 = "192.168.1.2";
        var endpoint = "login";

        // Exceed limit for first key
        for (int i = 0; i < 5; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key1, endpoint);
        }

        // Act
        var key1Allowed = await _rateLimitingService.IsRequestAllowedAsync(key1, endpoint);
        var key2Allowed = await _rateLimitingService.IsRequestAllowedAsync(key2, endpoint);

        // Assert
        key1Allowed.Should().BeFalse();
        key2Allowed.Should().BeTrue(); // Different key, should be allowed
    }

    [Fact]
    public async Task IsRequestAllowedAsync_UnknownEndpoint_UsesDefaultConfig()
    {
        // Arrange
        var key = "***********";
        var unknownEndpoint = "unknown-endpoint";

        // Record attempts for unknown endpoint (should use login config as default)
        for (int i = 0; i < 5; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key, unknownEndpoint);
        }

        // Act
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, unknownEndpoint);

        // Assert
        isAllowed.Should().BeFalse(); // Should use default login config (5 attempts max)
    }

    [Fact]
    public async Task RecordFailedAttemptAsync_RefreshEndpoint_UsesDifferentLimits()
    {
        // Arrange
        var key = "***********";
        var endpoint = "refresh";

        // Record attempts up to refresh limit (10)
        for (int i = 0; i < 9; i++)
        {
            await _rateLimitingService.RecordFailedAttemptAsync(key, endpoint);
        }

        // Act
        var isAllowed = await _rateLimitingService.IsRequestAllowedAsync(key, endpoint);

        // Assert
        isAllowed.Should().BeTrue(); // Should still be allowed (9 out of 10)
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task RecordFailedAttemptAsync_EmptyKey_DoesNotThrow(string? key)
    {
        // Act & Assert
        var action = async () => await _rateLimitingService.RecordFailedAttemptAsync(key!, "login");
        await action.Should().NotThrowAsync();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task RecordSuccessfulAttemptAsync_EmptyKey_DoesNotThrow(string? key)
    {
        // Act & Assert
        var action = async () => await _rateLimitingService.RecordSuccessfulAttemptAsync(key!, "login");
        await action.Should().NotThrowAsync();
    }

    public void Dispose()
    {
        _memoryCache.Dispose();
    }
}
