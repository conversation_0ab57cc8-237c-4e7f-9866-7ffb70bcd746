using FleetXQ.Api.Hubs;
using FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;
using FleetXQ.Application.Features.Telemetry.DTOs;
using FleetXQ.Application.Features.Vehicles.DTOs;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace FleetXQ.Api.Tests.Hubs;

public class TelemetryHubTests : SignalRHubTestBase
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly Mock<ILogger<TelemetryHub>> _mockLogger;
    private readonly TelemetryHub _hub;

    public TelemetryHubTests()
    {
        _mockMediator = new Mock<IMediator>();
        _mockLogger = CreateMockLogger<TelemetryHub>();

        _hub = new TelemetryHub(
            _mockMediator.Object,
            MockConnectionManager.Object,
            MockConnectionStateService.Object,
            _mockLogger.Object)
        {
            Context = MockContext.Object,
            Clients = MockClients.Object,
            Groups = MockGroups.Object
        };
    }

    [Fact]
    public async Task SubscribeToVehicle_WithValidVehicleId_ShouldAddToGroupAndSendConfirmation()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var expectedGroupName = $"Vehicle_{vehicleId}";

        // Act
        await _hub.SubscribeToVehicle(vehicleId);

        // Assert
        VerifyAddedToGroup(expectedGroupName);
        VerifyUserAddedToGroup(expectedGroupName);
        VerifyClientMethodCalled("SubscriptionConfirmed");
    }

    [Fact]
    public async Task SubscribeToVehicle_WithEmptyVehicleId_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _hub.SubscribeToVehicle(vehicleId));
    }

    [Fact]
    public async Task SubscribeToVehicle_WithValidVehicleId_ShouldSendLatestTelemetryData()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var telemetryData = new TelemetryDto
        {
            VehicleId = vehicleId,
            VehicleName = "Test Vehicle",
            Location = new LocationDto { Latitude = 40.7128m, Longitude = -74.0060m },
            SpeedKmh = 60,
            Timestamp = DateTime.UtcNow
        };

        var queryResult = GetLatestTelemetryResult.CreateSuccess(telemetryData);
        _mockMediator.Setup(m => m.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResult);

        // Act
        await _hub.SubscribeToVehicle(vehicleId);

        // Assert
        _mockMediator.Verify(m => m.Send(It.Is<GetLatestTelemetryQuery>(q => 
            q.VehicleId == vehicleId && q.MaxAgeHours == 1), It.IsAny<CancellationToken>()), Times.Once);
        VerifyClientMethodCalled("TelemetryUpdate");
    }

    [Fact]
    public async Task UnsubscribeFromVehicle_WithValidVehicleId_ShouldRemoveFromGroupAndSendConfirmation()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var expectedGroupName = $"Vehicle_{vehicleId}";

        // Act
        await _hub.UnsubscribeFromVehicle(vehicleId);

        // Assert
        VerifyRemovedFromGroup(expectedGroupName);
        VerifyUserRemovedFromGroup(expectedGroupName);
        VerifyClientMethodCalled("UnsubscriptionConfirmed");
    }

    [Fact]
    public async Task UnsubscribeFromVehicle_WithEmptyVehicleId_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _hub.UnsubscribeFromVehicle(vehicleId));
    }

    [Fact]
    public async Task GetVehicleStatus_WithValidVehicleId_ShouldReturnOnlineStatus()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var telemetryData = new TelemetryDto
        {
            VehicleId = vehicleId,
            VehicleName = "Test Vehicle",
            Location = new LocationDto { Latitude = 40.7128m, Longitude = -74.0060m },
            SpeedKmh = 60,
            Timestamp = DateTime.UtcNow.AddMinutes(-5), // 5 minutes ago
            IsMoving = true
        };

        var queryResult = GetLatestTelemetryResult.CreateSuccess(telemetryData);
        _mockMediator.Setup(m => m.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResult);

        // Act
        await _hub.GetVehicleStatus(vehicleId);

        // Assert
        _mockMediator.Verify(m => m.Send(It.Is<GetLatestTelemetryQuery>(q => 
            q.VehicleId == vehicleId && q.MaxAgeHours == 24), It.IsAny<CancellationToken>()), Times.Once);
        
        MockCaller.Verify(c => c.SendAsync("VehicleStatus", It.Is<object>(data => 
            data.GetType().GetProperty("Status")!.GetValue(data)!.ToString() == "Active" &&
            data.GetType().GetProperty("IsOnline")!.GetValue(data)!.Equals(true)), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetVehicleStatus_WithNoTelemetryData_ShouldReturnOfflineStatus()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var queryResult = GetLatestTelemetryResult.NotFound();
        
        _mockMediator.Setup(m => m.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResult);

        // Act
        await _hub.GetVehicleStatus(vehicleId);

        // Assert
        MockCaller.Verify(c => c.SendAsync("VehicleStatus", It.Is<object>(data => 
            data.GetType().GetProperty("Status")!.GetValue(data)!.ToString() == "Offline" &&
            data.GetType().GetProperty("IsOnline")!.GetValue(data)!.Equals(false)), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetVehicleStatus_WithEmptyVehicleId_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _hub.GetVehicleStatus(vehicleId));
    }

    [Fact]
    public async Task OnConnectedAsync_ShouldRegisterConnectionAndLogInfo()
    {
        // Act
        await _hub.OnConnectedAsync();

        // Assert
        VerifyConnectionAdded();
        MockConnectionStateService.Verify(cs => cs.OnConnectionEstablishedAsync(
            TestConnectionId, TestUserId, "TelemetryHub", It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task OnDisconnectedAsync_ShouldUnregisterConnectionAndCleanupGroups()
    {
        // Arrange
        var userGroups = new[] { "Vehicle_123", "Vehicle_456" };
        MockConnectionManager.Setup(cm => cm.GetUserGroupsAsync(TestUserId))
            .ReturnsAsync(userGroups);

        // Act
        await _hub.OnDisconnectedAsync(null);

        // Assert
        VerifyConnectionRemoved();
        MockConnectionStateService.Verify(cs => cs.OnConnectionTerminatedAsync(TestConnectionId, null), Times.Once);
        
        foreach (var group in userGroups)
        {
            VerifyUserRemovedFromGroup(group);
        }
    }

    [Fact]
    public async Task OnDisconnectedAsync_WithException_ShouldLogWarningAndCleanup()
    {
        // Arrange
        var exception = new Exception("Connection lost");
        var userGroups = new[] { "Vehicle_123" };
        MockConnectionManager.Setup(cm => cm.GetUserGroupsAsync(TestUserId))
            .ReturnsAsync(userGroups);

        // Act
        await _hub.OnDisconnectedAsync(exception);

        // Assert
        MockConnectionStateService.Verify(cs => cs.OnConnectionTerminatedAsync(TestConnectionId, exception), Times.Once);
    }

    [Fact]
    public async Task SubscribeToVehicle_WhenMediatorThrowsException_ShouldSendErrorToClient()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        _mockMediator.Setup(m => m.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        await _hub.SubscribeToVehicle(vehicleId);

        // Assert
        // Should still add to group despite error in sending latest data
        VerifyAddedToGroup($"Vehicle_{vehicleId}");
        VerifyClientMethodCalled("SubscriptionConfirmed");
    }

    [Theory]
    [InlineData("Admin")]
    [InlineData("Manager")]
    [InlineData("Driver")]
    [InlineData("User")]
    public async Task SubscribeToVehicle_WithDifferentRoles_ShouldAllowAccess(string role)
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal(role));
        var vehicleId = Guid.NewGuid();

        // Act
        await _hub.SubscribeToVehicle(vehicleId);

        // Assert
        VerifyAddedToGroup($"Vehicle_{vehicleId}");
        VerifyClientMethodCalled("SubscriptionConfirmed");
    }
}
