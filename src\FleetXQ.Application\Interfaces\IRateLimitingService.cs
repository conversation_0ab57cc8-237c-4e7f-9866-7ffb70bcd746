namespace FleetXQ.Application.Interfaces;

/// <summary>
/// Service for rate limiting authentication attempts
/// </summary>
public interface IRateLimitingService
{
    /// <summary>
    /// Checks if a request is allowed based on rate limiting rules
    /// </summary>
    /// <param name="key">The rate limiting key (e.g., IP address, user ID)</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>True if the request is allowed</returns>
    Task<bool> IsRequestAllowedAsync(string key, string endpoint);

    /// <summary>
    /// Records a failed authentication attempt
    /// </summary>
    /// <param name="key">The rate limiting key</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task RecordFailedAttemptAsync(string key, string endpoint);

    /// <summary>
    /// Records a successful authentication attempt
    /// </summary>
    /// <param name="key">The rate limiting key</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task RecordSuccessfulAttemptAsync(string key, string endpoint);

    /// <summary>
    /// Gets the remaining time until rate limit reset
    /// </summary>
    /// <param name="key">The rate limiting key</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>The remaining time until reset</returns>
    Task<TimeSpan?> GetRemainingLockoutTimeAsync(string key, string endpoint);
}
