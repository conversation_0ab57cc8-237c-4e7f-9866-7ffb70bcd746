# FleetXQ API Integration Tests

This document describes the comprehensive integration testing setup for the FleetXQ API, implemented according to **Prompt 3.2: Integration Testing Setup** specifications.

## Overview

The integration testing infrastructure provides:
- Complete API endpoint testing with real HTTP requests
- SignalR real-time functionality testing
- In-memory database for test isolation
- Authentication and authorization testing
- Performance and load testing capabilities
- Test data builders for consistent test scenarios

## Architecture

### Core Components

1. **IntegrationTestWebApplicationFactory** - Custom WebApplicationFactory for test environment setup
2. **ApiIntegrationTestBase** - Base class for API endpoint testing
3. **SignalRIntegrationTestBase** - Base class for SignalR hub testing (existing, enhanced)
4. **TestDataBuilder** - Builders for creating test entities
5. **Performance Tests** - Load testing with NBomber

### Database Setup

- **In-Memory Database**: Uses Entity Framework InMemory provider for fast, isolated tests
- **ApplicationDbContext**: Full EF Core context with all entities configured
- **Test Isolation**: Each test gets a clean database state
- **Seeding Support**: Easy test data seeding with async support

## Usage

### Basic API Integration Test

```csharp
public class MyApiTests : ApiIntegrationTestBase
{
    public MyApiTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task GetVehicles_WithAuth_ShouldReturnVehicles()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var vehicles = TestDataBuilder.CreateVehicles(5);

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
            context.Vehicles.AddRange(vehicles);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetVehicleListQuery, PaginatedApiResponse<VehicleListDto>>(
            "/api/vehicles/list",
            new GetVehicleListQuery { PageNumber = 1, PageSize = 10 }
        );

        // Assert
        AssertSuccessResponse(response, content);
        content!.Data!.Items.Should().HaveCount(5);
    }
}
```

### SignalR Integration Test

```csharp
public class MySignalRTests : SignalRIntegrationTestBase
{
    [Fact]
    public async Task TelemetryHub_JoinGroup_ShouldReceiveUpdates()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, "testuser", "User");
        
        var connection = await StartConnectionAsync("/hubs/telemetry", token);
        var updateReceived = false;

        connection.On<object>("TelemetryUpdate", (data) => updateReceived = true);

        // Act
        await connection.InvokeAsync("JoinVehicleGroup", vehicleId.ToString());
        
        // Simulate broadcast
        var hubContext = Factory.Services.GetRequiredService<IHubContext<TelemetryHub>>();
        await hubContext.Clients.Group($"vehicle_{vehicleId}").SendAsync("TelemetryUpdate", new { VehicleId = vehicleId });

        // Assert
        await Task.Delay(1000);
        updateReceived.Should().BeTrue();
    }
}
```

## Test Data Builders

### Creating Test Entities

```csharp
// Create users with different roles
var adminUser = TestDataBuilder.CreateAdminUser();
var managerUser = TestDataBuilder.CreateManagerUser();
var regularUser = TestDataBuilder.CreateUser(role: UserRole.User);

// Create vehicles with specific properties
var vehicle = TestDataBuilder.CreateVehicle(
    name: "Test Vehicle",
    licensePlate: "TEST-123",
    status: VehicleStatus.Available
);

// Create drivers
var driver = TestDataBuilder.CreateDriver(
    firstName: "John",
    lastName: "Doe",
    status: DriverStatus.Available
);

// Create alerts and trips
var alert = TestDataBuilder.CreateAlert(vehicle.Id, AlertType.SpeedingViolation);
var trip = TestDataBuilder.CreateTrip(vehicle.Id, driver.Id);
```

## Authentication Testing

### JWT Token Creation

```csharp
// Create authenticated client
var client = CreateAuthenticatedClient(
    userId: user.Id,
    username: user.Username,
    roles: new[] { user.Role.ToString() }
);

// Or set authorization header manually
var token = CreateTestJwtToken(user.Id, user.Username, user.Email, roles);
SetAuthorizationHeader(token);
```

### Authorization Scenarios

- **Admin Access**: Tests for admin-only endpoints
- **Manager Access**: Tests for manager-level permissions
- **User Access**: Tests for regular user permissions
- **Unauthorized Access**: Tests for proper 401/403 responses
- **Token Validation**: Tests for expired/invalid tokens

## Performance Testing

### Load Testing with NBomber

```csharp
[Fact]
public async Task GetVehicles_LoadTest_ShouldHandleConcurrentRequests()
{
    var scenario = Scenario.Create("get_vehicles_load_test", async context =>
    {
        // Test logic here
        return Response.Ok();
    })
    .WithLoadSimulations(
        Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromSeconds(30))
    );

    var stats = NBomberRunner.RegisterScenarios(scenario).Run();
    
    // Assert performance metrics
    stats.AllScenarios.First().Ok.Request.RPS.Should().BeGreaterThan(8);
}
```

## Test Categories

### 1. Authentication Tests (`AuthenticationIntegrationTests`)
- Login with valid/invalid credentials
- Token validation and expiration
- Account lockout scenarios
- Role-based access control

### 2. Vehicle API Tests (`VehiclesIntegrationTests`)
- CRUD operations
- Status updates
- Pagination
- Authorization checks
- Data validation

### 3. Driver API Tests (`DriversIntegrationTests`)
- Driver management
- Status updates
- Search and filtering
- Permission validation

### 4. SignalR Tests (`TelemetryHubIntegrationTests`)
- Connection establishment
- Group subscription/unsubscription
- Real-time message delivery
- Authentication validation
- Concurrent connections

### 5. Performance Tests (`ApiPerformanceTests`)
- Load testing scenarios
- Database stress testing
- Mixed workload simulation
- Latency and throughput validation

## Running Tests

### Command Line
```bash
# Run all integration tests
dotnet test tests/FleetXQ.Api.Tests --filter Category=Integration

# Run specific test class
dotnet test tests/FleetXQ.Api.Tests --filter ClassName=AuthenticationIntegrationTests

# Run performance tests
dotnet test tests/FleetXQ.Api.Tests --filter Category=Performance
```

### Test Validation
```bash
# Run the comprehensive test suite validation
dotnet test tests/FleetXQ.Api.Tests --filter ClassName=IntegrationTestRunner
```

## Configuration

### Test Settings
- **JWT Configuration**: Test-specific JWT settings with known keys
- **Database**: In-memory database with unique names per test run
- **Logging**: Reduced logging for test performance
- **Authentication**: Simplified auth for testing scenarios

### Environment Variables
- `ASPNETCORE_ENVIRONMENT=Testing`
- Custom configuration overrides for test scenarios

## Best Practices

### Test Isolation
- Each test gets a fresh database
- Use `SeedDatabaseAsync()` for test data setup
- Call `ClearDatabaseAsync()` in cleanup if needed

### Performance
- Tests run against in-memory database for speed
- Minimal logging configuration
- Efficient test data creation

### Maintainability
- Use TestDataBuilder for consistent entity creation
- Follow AAA pattern (Arrange, Act, Assert)
- Clear test names describing scenarios
- Proper cleanup in test disposal

## Validation Criteria (Prompt 3.2)

✅ **All API endpoints tested end-to-end**
- Authentication, Vehicles, Drivers, Alerts, Telemetry endpoints covered

✅ **SignalR real-time features work correctly**
- Connection establishment, group management, message delivery tested

✅ **Authentication and authorization tested**
- JWT token validation, role-based access, permission scenarios covered

✅ **Tests run in isolation without side effects**
- In-memory database, proper cleanup, independent test execution

✅ **Performance test scenarios included**
- Load testing, stress testing, concurrent user simulation

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure ApplicationDbContext is properly configured
2. **Authentication**: Verify JWT configuration matches test setup
3. **SignalR**: Check hub registration and connection setup
4. **Performance**: NBomber package must be installed for load tests

### Debug Tips
- Use `_output.WriteLine()` in tests for debugging
- Check test logs for detailed error information
- Verify test data seeding with database inspection
- Use breakpoints in test methods for step-through debugging
