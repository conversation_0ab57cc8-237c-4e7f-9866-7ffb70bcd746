using FleetXQ.Application.Features.Authentication.Commands.LoginUser;
using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Interfaces;
using FleetXQ.Infrastructure.Authentication;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace FleetXQ.Application.Tests.Integration;

/// <summary>
/// Integration tests for authentication flow
/// </summary>
public sealed class AuthenticationIntegrationTests
{
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly IPasswordHashingService _passwordHashingService;
    private readonly ITokenService _tokenService;
    private readonly IAuthorizationService _authorizationService;
    private readonly LoginUserCommandHandler _loginHandler;

    public AuthenticationIntegrationTests()
    {
        _mockUserRepository = new Mock<IUserRepository>();

        // Create real services for integration testing
        var mockConfig = new Mock<IConfiguration>();
        mockConfig.Setup(x => x["Jwt:Key"]).Returns("YourSuperSecretKeyThatIsAtLeast32CharactersLong!");
        mockConfig.Setup(x => x["Jwt:Issuer"]).Returns("FleetXQ");
        mockConfig.Setup(x => x["Jwt:Audience"]).Returns("FleetXQ-Users");
        mockConfig.Setup(x => x.GetValue<int>("Jwt:ExpiryInMinutes", 60)).Returns(60);
        mockConfig.Setup(x => x.GetValue<int>("Jwt:RefreshTokenExpiryInDays", 7)).Returns(7);

        var mockPasswordLogger = new Mock<ILogger<PasswordHashingService>>();
        var mockTokenLogger = new Mock<ILogger<TokenService>>();
        var mockAuthLogger = new Mock<ILogger<AuthorizationService>>();
        var mockLoginLogger = new Mock<ILogger<LoginUserCommandHandler>>();

        _passwordHashingService = new PasswordHashingService(mockPasswordLogger.Object);
        _tokenService = new TokenService(mockConfig.Object, mockTokenLogger.Object);
        _authorizationService = new AuthorizationService(_mockUserRepository.Object, mockAuthLogger.Object);

        _loginHandler = new LoginUserCommandHandler(
            _mockUserRepository.Object,
            _passwordHashingService,
            _tokenService,
            mockLoginLogger.Object);
    }

    [Fact]
    public async Task CompleteAuthenticationFlow_ValidUser_WorksEndToEnd()
    {
        // Arrange
        var password = "TestPassword123!";
        var hashedPassword = _passwordHashingService.HashPassword(password);
        
        var user = new User("testuser", "<EMAIL>", hashedPassword, "John", "Doe", UserRole.Manager);
        var userId = Guid.NewGuid();
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByUsernameAsync("testuser", It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);
        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        var loginCommand = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = password,
            ClientIpAddress = "***********",
            UserAgent = "Test Agent"
        };

        // Act - Login
        var loginResult = await _loginHandler.Handle(loginCommand, CancellationToken.None);

        // Assert - Login successful
        loginResult.Should().NotBeNull();
        loginResult.Success.Should().BeTrue();
        loginResult.AccessToken.Should().NotBeNullOrEmpty();
        loginResult.RefreshToken.Should().NotBeNullOrEmpty();
        loginResult.User.Should().NotBeNull();
        loginResult.User!.Username.Should().Be("testuser");
        loginResult.User.Role.Should().Be("Manager");

        // Act - Validate token
        var isTokenValid = _tokenService.ValidateToken(loginResult.AccessToken!);
        var userIdFromToken = _tokenService.GetUserIdFromToken(loginResult.AccessToken!);

        // Assert - Token validation
        isTokenValid.Should().BeTrue();
        userIdFromToken.Should().Be(userId);

        // Act - Check authorization
        var hasManagerRole = await _authorizationService.HasRoleAsync(userId, UserRole.Manager);
        var hasAdminRole = await _authorizationService.HasRoleAsync(userId, UserRole.Admin);
        var hasVehicleReadPermission = await _authorizationService.HasPermissionAsync(userId, "vehicles.read");
        var hasSystemAdminPermission = await _authorizationService.HasPermissionAsync(userId, "system.admin");

        // Assert - Authorization
        hasManagerRole.Should().BeTrue();
        hasAdminRole.Should().BeFalse(); // Manager doesn't have Admin role
        hasVehicleReadPermission.Should().BeTrue(); // Manager has vehicle read permission
        hasSystemAdminPermission.Should().BeFalse(); // Manager doesn't have system admin permission
    }

    [Fact]
    public async Task AuthenticationFlow_InvalidPassword_FailsCorrectly()
    {
        // Arrange
        var correctPassword = "TestPassword123!";
        var wrongPassword = "WrongPassword123!";
        var hashedPassword = _passwordHashingService.HashPassword(correctPassword);
        
        var user = new User("testuser", "<EMAIL>", hashedPassword, "John", "Doe", UserRole.User);

        _mockUserRepository.Setup(x => x.GetByUsernameAsync("testuser", It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        var loginCommand = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = wrongPassword
        };

        // Act
        var loginResult = await _loginHandler.Handle(loginCommand, CancellationToken.None);

        // Assert
        loginResult.Should().NotBeNull();
        loginResult.Success.Should().BeFalse();
        loginResult.ErrorMessage.Should().Be("Invalid username/email or password");
        loginResult.AccessToken.Should().BeNull();
        loginResult.RefreshToken.Should().BeNull();
        loginResult.User.Should().BeNull();
    }

    [Theory]
    [InlineData(UserRole.Admin, "system.admin", true)]
    [InlineData(UserRole.Manager, "vehicles.write", true)]
    [InlineData(UserRole.Driver, "vehicles.read", true)]
    [InlineData(UserRole.User, "vehicles.write", false)]
    [InlineData(UserRole.Driver, "users.read", false)]
    public async Task AuthorizationService_DifferentRolesAndPermissions_WorksCorrectly(
        UserRole userRole, string permission, bool expectedResult)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", userRole);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        _mockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var hasPermission = await _authorizationService.HasPermissionAsync(userId, permission);

        // Assert
        hasPermission.Should().Be(expectedResult);
    }

    [Fact]
    public void TokenService_GenerateAndValidateToken_WorksCorrectly()
    {
        // Arrange
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.Admin);
        var userId = Guid.NewGuid();
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        // Act
        var accessToken = _tokenService.GenerateAccessToken(user);
        var refreshToken = _tokenService.GenerateRefreshToken();
        var secureRefreshToken = _tokenService.GenerateSecureRefreshToken(userId);

        var isTokenValid = _tokenService.ValidateToken(accessToken);
        var userIdFromToken = _tokenService.GetUserIdFromToken(accessToken);
        var tokenExpiry = _tokenService.GetTokenExpiry(accessToken);

        // Assert
        accessToken.Should().NotBeNullOrEmpty();
        refreshToken.Should().NotBeNullOrEmpty();
        secureRefreshToken.Should().NotBeNullOrEmpty();
        
        isTokenValid.Should().BeTrue();
        userIdFromToken.Should().Be(userId);
        tokenExpiry.Should().NotBeNull();
        tokenExpiry.Should().BeAfter(DateTime.UtcNow);

        // Refresh token should be different from secure refresh token
        refreshToken.Should().NotBe(secureRefreshToken);
        
        // Secure refresh token should be longer (more entropy)
        secureRefreshToken.Length.Should().BeGreaterThan(refreshToken.Length);
    }

    [Fact]
    public void PasswordHashingService_HashAndVerify_WorksCorrectly()
    {
        // Arrange
        var password = "TestPassword123!";

        // Act
        var hashedPassword = _passwordHashingService.HashPassword(password);
        var isValidPassword = _passwordHashingService.VerifyPassword(password, hashedPassword);
        var isInvalidPassword = _passwordHashingService.VerifyPassword("WrongPassword", hashedPassword);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        hashedPassword.Should().NotBe(password);
        hashedPassword.Should().StartWith("$2a$"); // BCrypt format

        isValidPassword.Should().BeTrue();
        isInvalidPassword.Should().BeFalse();
    }

    [Fact]
    public async Task AuthorizationService_UserContext_WorksCorrectly()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.Manager);
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        // Generate a real token to get real claims
        var token = _tokenService.GenerateAccessToken(user);
        var tokenHandler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);
        var claimsPrincipal = new System.Security.Claims.ClaimsPrincipal(
            new System.Security.Claims.ClaimsIdentity(jsonToken.Claims));

        // Act
        var userContext = _authorizationService.ValidateUserContext(claimsPrincipal);

        // Assert
        userContext.Should().NotBeNull();
        userContext!.UserId.Should().Be(userId);
        userContext.Username.Should().Be("testuser");
        userContext.Role.Should().Be(UserRole.Manager);
        userContext.IsActive.Should().BeTrue();
        userContext.Permissions.Should().NotBeEmpty();
        userContext.Permissions.Should().Contain("vehicles.read");
        userContext.Permissions.Should().Contain("vehicles.write");
        userContext.Permissions.Should().NotContain("system.admin");
    }
}
