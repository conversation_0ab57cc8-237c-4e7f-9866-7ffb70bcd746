using AutoMapper;
using FleetXQ.Application.Common.Mappings;
using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Interfaces;
using FleetXQ.Domain.Services;
using Microsoft.Extensions.Logging;
using Moq;

namespace FleetXQ.Application.Tests.Common;

/// <summary>
/// Base class for application tests providing common setup and utilities
/// </summary>
public abstract class TestBase
{
    protected readonly IMapper Mapper;
    protected readonly Mock<IVehicleRepository> MockVehicleRepository;
    protected readonly Mock<IUserRepository> MockUserRepository;
    protected readonly Mock<IAlertRepository> MockAlertRepository;
    protected readonly Mock<IDriverRepository> MockDriverRepository;
    protected readonly Mock<IPasswordHashingService> MockPasswordHashingService;
    protected readonly Mock<ITokenService> MockTokenService;
    protected readonly Mock<IAlertEvaluationService> MockAlertEvaluationService;
    protected readonly Mock<ISignalRNotificationService> MockSignalRNotificationService;

    protected TestBase()
    {
        // Setup AutoMapper
        var mapperConfig = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<MappingProfile>();
        });
        Mapper = mapperConfig.CreateMapper();

        // Setup repository mocks
        MockVehicleRepository = new Mock<IVehicleRepository>();
        MockUserRepository = new Mock<IUserRepository>();
        MockAlertRepository = new Mock<IAlertRepository>();
        MockDriverRepository = new Mock<IDriverRepository>();

        // Setup service mocks
        MockPasswordHashingService = new Mock<IPasswordHashingService>();
        MockTokenService = new Mock<ITokenService>();
        MockAlertEvaluationService = new Mock<IAlertEvaluationService>();
        MockSignalRNotificationService = new Mock<ISignalRNotificationService>();
    }

    /// <summary>
    /// Creates a mock logger for the specified type
    /// </summary>
    /// <typeparam name="T">The type to create a logger for</typeparam>
    /// <returns>A mock logger</returns>
    protected static Mock<ILogger<T>> CreateMockLogger<T>()
    {
        return new Mock<ILogger<T>>();
    }

    /// <summary>
    /// Verifies that a logger was called with the specified log level
    /// </summary>
    /// <typeparam name="T">The logger type</typeparam>
    /// <param name="mockLogger">The mock logger</param>
    /// <param name="logLevel">The expected log level</param>
    /// <param name="times">The expected number of times (default: once)</param>
    protected static void VerifyLoggerCalled<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, Times? times = null)
    {
        mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => true),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            times ?? Times.Once);
    }

    /// <summary>
    /// Creates a cancellation token for testing
    /// </summary>
    /// <returns>A cancellation token</returns>
    protected static CancellationToken CreateCancellationToken()
    {
        return new CancellationTokenSource().Token;
    }

    /// <summary>
    /// Creates a cancelled cancellation token for testing timeout scenarios
    /// </summary>
    /// <returns>A cancelled cancellation token</returns>
    protected static CancellationToken CreateCancelledCancellationToken()
    {
        var cts = new CancellationTokenSource();
        cts.Cancel();
        return cts.Token;
    }

    /// <summary>
    /// Resets all mock repositories to their default state
    /// </summary>
    protected void ResetAllMocks()
    {
        MockVehicleRepository.Reset();
        MockUserRepository.Reset();
        MockAlertRepository.Reset();
        MockDriverRepository.Reset();
        MockPasswordHashingService.Reset();
        MockTokenService.Reset();
        MockAlertEvaluationService.Reset();
        MockSignalRNotificationService.Reset();
    }

    /// <summary>
    /// Verifies that no unexpected calls were made to any repository
    /// </summary>
    protected void VerifyNoUnexpectedRepositoryCalls()
    {
        MockVehicleRepository.VerifyNoOtherCalls();
        MockUserRepository.VerifyNoOtherCalls();
        MockAlertRepository.VerifyNoOtherCalls();
        MockDriverRepository.VerifyNoOtherCalls();
    }
}
