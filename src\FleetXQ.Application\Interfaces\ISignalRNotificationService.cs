using FleetXQ.Application.Features.Telemetry.DTOs;
using FleetXQ.Application.Features.Alerts.DTOs;

namespace FleetXQ.Application.Interfaces;

/// <summary>
/// Interface for sending SignalR notifications
/// </summary>
public interface ISignalRNotificationService
{
    /// <summary>
    /// Sends telemetry update to vehicle subscribers
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="telemetryData">The telemetry data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendTelemetryUpdateAsync(Guid vehicleId, TelemetryDataDto telemetryData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends alert notification to alert subscribers
    /// </summary>
    /// <param name="alert">The alert data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendAlertNotificationAsync(AlertDto alert, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends vehicle status update to dashboard subscribers
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="status">The vehicle status</param>
    /// <param name="lastUpdate">The last update timestamp</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendVehicleStatusUpdateAsync(Guid vehicleId, string status, DateTime lastUpdate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends dashboard metrics update
    /// </summary>
    /// <param name="metrics">The dashboard metrics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendDashboardMetricsUpdateAsync(object metrics, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends notification to specific user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="method">The method name</param>
    /// <param name="data">The data to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendToUserAsync(Guid userId, string method, object data, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends notification to specific group
    /// </summary>
    /// <param name="groupName">The group name</param>
    /// <param name="method">The method name</param>
    /// <param name="data">The data to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendToGroupAsync(string groupName, string method, object data, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends notification to all connected clients
    /// </summary>
    /// <param name="method">The method name</param>
    /// <param name="data">The data to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendToAllAsync(string method, object data, CancellationToken cancellationToken = default);
}
