namespace FleetXQ.Application.Interfaces;

/// <summary>
/// Interface for managing SignalR connections and user mappings
/// </summary>
public interface ISignalRConnectionManager
{
    /// <summary>
    /// Adds a connection for a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="connectionId">The connection ID</param>
    Task AddConnectionAsync(Guid userId, string connectionId);

    /// <summary>
    /// Removes a connection for a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="connectionId">The connection ID</param>
    Task RemoveConnectionAsync(Guid userId, string connectionId);

    /// <summary>
    /// Gets all connection IDs for a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>Collection of connection IDs</returns>
    Task<IEnumerable<string>> GetConnectionsAsync(Guid userId);

    /// <summary>
    /// Gets the user ID for a connection
    /// </summary>
    /// <param name="connectionId">The connection ID</param>
    /// <returns>The user ID if found, null otherwise</returns>
    Task<Guid?> GetUserIdAsync(string connectionId);

    /// <summary>
    /// Gets all users connected to a specific group
    /// </summary>
    /// <param name="groupName">The group name</param>
    /// <returns>Collection of user IDs</returns>
    Task<IEnumerable<Guid>> GetGroupUsersAsync(string groupName);

    /// <summary>
    /// Adds a user to a group
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="groupName">The group name</param>
    Task AddToGroupAsync(Guid userId, string groupName);

    /// <summary>
    /// Removes a user from a group
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="groupName">The group name</param>
    Task RemoveFromGroupAsync(Guid userId, string groupName);

    /// <summary>
    /// Gets all groups a user belongs to
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>Collection of group names</returns>
    Task<IEnumerable<string>> GetUserGroupsAsync(Guid userId);

    /// <summary>
    /// Checks if a user is connected
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>True if the user has active connections</returns>
    Task<bool> IsUserConnectedAsync(Guid userId);

    /// <summary>
    /// Gets the total number of active connections
    /// </summary>
    /// <returns>The number of active connections</returns>
    Task<int> GetConnectionCountAsync();
}
