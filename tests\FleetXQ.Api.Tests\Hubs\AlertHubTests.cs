using FleetXQ.Api.Hubs;
using FleetXQ.Domain.Enums;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace FleetXQ.Api.Tests.Hubs;

public class AlertHubTests : SignalRHubTestBase
{
    private readonly Mock<ILogger<AlertHub>> _mockLogger;
    private readonly AlertHub _hub;

    public AlertHubTests()
    {
        _mockLogger = CreateMockLogger<AlertHub>();

        _hub = new AlertHub(
            MockConnectionManager.Object,
            MockConnectionStateService.Object,
            _mockLogger.Object)
        {
            Context = MockContext.Object,
            Clients = MockClients.Object,
            Groups = MockGroups.Object
        };
    }

    [Fact]
    public async Task SubscribeToAlerts_WithoutFilters_ShouldAddToDefaultGroupAndSendConfirmation()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("User"));

        // Act
        await _hub.SubscribeToAlerts();

        // Assert
        var expectedGroupName = "Alerts_User";
        VerifyAddedToGroup(expectedGroupName);
        VerifyUserAddedToGroup(expectedGroupName);
        VerifyClientMethodCalled("AlertSubscriptionConfirmed");
    }

    [Fact]
    public async Task SubscribeToAlerts_WithSeverityFilter_ShouldAddToSeveritySpecificGroup()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("Manager"));
        var severityFilter = "Critical";

        // Act
        await _hub.SubscribeToAlerts(severityFilter);

        // Assert
        var expectedGroupName = "Alerts_Severity_Critical_Manager";
        VerifyAddedToGroup(expectedGroupName);
        VerifyUserAddedToGroup(expectedGroupName);
        VerifyClientMethodCalled("AlertSubscriptionConfirmed");
    }

    [Fact]
    public async Task SubscribeToAlerts_WithTypeFilter_ShouldAddToTypeSpecificGroup()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("Admin"));
        var typeFilter = "Speed";

        // Act
        await _hub.SubscribeToAlerts(null, typeFilter);

        // Assert
        var expectedGroupName = "Alerts_Type_Speed_Admin";
        VerifyAddedToGroup(expectedGroupName);
        VerifyUserAddedToGroup(expectedGroupName);
        VerifyClientMethodCalled("AlertSubscriptionConfirmed");
    }

    [Fact]
    public async Task SubscribeToAlerts_WithBothFilters_ShouldAddToFilteredGroup()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("Manager"));
        var severityFilter = "High";
        var typeFilter = "Fuel";

        // Act
        await _hub.SubscribeToAlerts(severityFilter, typeFilter);

        // Assert
        var expectedGroupName = "Alerts_Severity_High_Type_Fuel_Manager";
        VerifyAddedToGroup(expectedGroupName);
        VerifyUserAddedToGroup(expectedGroupName);
        VerifyClientMethodCalled("AlertSubscriptionConfirmed");
    }

    [Theory]
    [InlineData("Admin")]
    [InlineData("Manager")]
    [InlineData("Driver")]
    [InlineData("User")]
    public async Task SubscribeToAlerts_WithDifferentRoles_ShouldCreateRoleSpecificGroups(string role)
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal(role));

        // Act
        await _hub.SubscribeToAlerts();

        // Assert
        var expectedGroupName = $"Alerts_{role}";
        VerifyAddedToGroup(expectedGroupName);
        VerifyUserAddedToGroup(expectedGroupName);
    }

    [Fact]
    public async Task UnsubscribeFromAlerts_WithoutFilters_ShouldRemoveFromDefaultGroup()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("User"));

        // Act
        await _hub.UnsubscribeFromAlerts();

        // Assert
        var expectedGroupName = "Alerts_User";
        VerifyRemovedFromGroup(expectedGroupName);
        VerifyUserRemovedFromGroup(expectedGroupName);
        VerifyClientMethodCalled("AlertUnsubscriptionConfirmed");
    }

    [Fact]
    public async Task UnsubscribeFromAlerts_WithFilters_ShouldRemoveFromFilteredGroup()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("Manager"));
        var severityFilter = "Critical";

        // Act
        await _hub.UnsubscribeFromAlerts(severityFilter);

        // Assert
        var expectedGroupName = "Alerts_Severity_Critical_Manager";
        VerifyRemovedFromGroup(expectedGroupName);
        VerifyUserRemovedFromGroup(expectedGroupName);
        VerifyClientMethodCalled("AlertUnsubscriptionConfirmed");
    }

    [Fact]
    public async Task SubscribeToVehicleAlerts_WithValidVehicleId_ShouldAddToVehicleSpecificGroup()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();

        // Act
        await _hub.SubscribeToVehicleAlerts(vehicleId);

        // Assert
        var expectedGroupName = $"VehicleAlerts_{vehicleId}";
        VerifyAddedToGroup(expectedGroupName);
        VerifyUserAddedToGroup(expectedGroupName);
        VerifyClientMethodCalled("VehicleAlertSubscriptionConfirmed");
    }

    [Fact]
    public async Task SubscribeToVehicleAlerts_WithEmptyVehicleId_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _hub.SubscribeToVehicleAlerts(vehicleId));
    }

    [Fact]
    public async Task UnsubscribeFromVehicleAlerts_WithValidVehicleId_ShouldRemoveFromVehicleSpecificGroup()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();

        // Act
        await _hub.UnsubscribeFromVehicleAlerts(vehicleId);

        // Assert
        var expectedGroupName = $"VehicleAlerts_{vehicleId}";
        VerifyRemovedFromGroup(expectedGroupName);
        VerifyUserRemovedFromGroup(expectedGroupName);
        VerifyClientMethodCalled("VehicleAlertUnsubscriptionConfirmed");
    }

    [Fact]
    public async Task UnsubscribeFromVehicleAlerts_WithEmptyVehicleId_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _hub.UnsubscribeFromVehicleAlerts(vehicleId));
    }

    [Fact]
    public async Task OnConnectedAsync_ShouldRegisterConnectionAndLogInfo()
    {
        // Act
        await _hub.OnConnectedAsync();

        // Assert
        VerifyConnectionAdded();
        MockConnectionStateService.Verify(cs => cs.OnConnectionEstablishedAsync(
            TestConnectionId, TestUserId, "AlertHub", It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task OnDisconnectedAsync_ShouldUnregisterConnectionAndCleanupGroups()
    {
        // Arrange
        var userGroups = new[] { "Alerts_Critical", "VehicleAlerts_123" };
        MockConnectionManager.Setup(cm => cm.GetUserGroupsAsync(TestUserId))
            .ReturnsAsync(userGroups);

        // Act
        await _hub.OnDisconnectedAsync(null);

        // Assert
        VerifyConnectionRemoved();
        MockConnectionStateService.Verify(cs => cs.OnConnectionTerminatedAsync(TestConnectionId, null), Times.Once);
        
        foreach (var group in userGroups)
        {
            VerifyUserRemovedFromGroup(group);
        }
    }

    [Fact]
    public async Task OnDisconnectedAsync_WithException_ShouldLogWarningAndCleanup()
    {
        // Arrange
        var exception = new Exception("Connection lost");
        var userGroups = new[] { "Alerts_User" };
        MockConnectionManager.Setup(cm => cm.GetUserGroupsAsync(TestUserId))
            .ReturnsAsync(userGroups);

        // Act
        await _hub.OnDisconnectedAsync(exception);

        // Assert
        MockConnectionStateService.Verify(cs => cs.OnConnectionTerminatedAsync(TestConnectionId, exception), Times.Once);
    }

    [Fact]
    public void DetermineAlertGroup_WithDriverRole_ShouldIncludeDriverInGroupName()
    {
        // This tests the private method indirectly through SubscribeToAlerts
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("Driver"));

        // Act
        var task = _hub.SubscribeToAlerts();

        // Assert
        task.Should().NotThrow();
        // The group name should contain "Driver"
        VerifyAddedToGroup("Alerts_Driver");
    }

    [Fact]
    public async Task SubscribeToAlerts_WithInvalidSeverityFilter_ShouldStillSubscribe()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("User"));
        var invalidSeverityFilter = "InvalidSeverity";

        // Act
        await _hub.SubscribeToAlerts(invalidSeverityFilter);

        // Assert
        // Should still subscribe to base group even with invalid filter
        VerifyAddedToGroup("Alerts_User");
        VerifyClientMethodCalled("AlertSubscriptionConfirmed");
    }

    [Fact]
    public async Task SubscribeToAlerts_WithInvalidTypeFilter_ShouldStillSubscribe()
    {
        // Arrange
        SetupHubContext(CreateTestClaimsPrincipal("User"));
        var invalidTypeFilter = "InvalidType";

        // Act
        await _hub.SubscribeToAlerts(null, invalidTypeFilter);

        // Assert
        // Should still subscribe to base group even with invalid filter
        VerifyAddedToGroup("Alerts_User");
        VerifyClientMethodCalled("AlertSubscriptionConfirmed");
    }
}
