using Bogus;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Api.Tests.Builders;

/// <summary>
/// Builder for creating test data entities
/// </summary>
public static class TestDataBuilder
{
    private static readonly Faker Faker = new();

    /// <summary>
    /// Creates a test user with specified properties
    /// </summary>
    /// <param name="username">The username</param>
    /// <param name="email">The email</param>
    /// <param name="role">The user role</param>
    /// <param name="isActive">Whether the user is active</param>
    /// <param name="isEmailConfirmed">Whether the email is confirmed</param>
    /// <returns>A test user</returns>
    public static User CreateUser(
        string? username = null,
        string? email = null,
        UserRole role = UserRole.User,
        bool isActive = true,
        bool isEmailConfirmed = true)
    {
        var actualUsername = username ?? Faker.Internet.UserName();
        var actualEmail = email ?? Faker.Internet.Email();
        var firstName = Faker.Name.FirstName();
        var lastName = Faker.Name.LastName();
        var passwordHash = "$2a$11$dummy.hash.for.testing.purposes.only";

        var user = new User(actualUsername, actualEmail, passwordHash, firstName, lastName, role);
        
        if (!isActive)
        {
            user.Deactivate();
        }
        
        if (isEmailConfirmed)
        {
            user.GenerateEmailConfirmationToken(Guid.NewGuid().ToString());
            user.ConfirmEmail(user.EmailConfirmationToken!);
        }

        return user;
    }

    /// <summary>
    /// Creates a test admin user
    /// </summary>
    /// <param name="username">The username</param>
    /// <param name="email">The email</param>
    /// <returns>A test admin user</returns>
    public static User CreateAdminUser(string? username = null, string? email = null)
    {
        return CreateUser(username, email, UserRole.Admin);
    }

    /// <summary>
    /// Creates a test manager user
    /// </summary>
    /// <param name="username">The username</param>
    /// <param name="email">The email</param>
    /// <returns>A test manager user</returns>
    public static User CreateManagerUser(string? username = null, string? email = null)
    {
        return CreateUser(username, email, UserRole.Manager);
    }

    /// <summary>
    /// Creates a test vehicle with specified properties
    /// </summary>
    /// <param name="vehicleName">The vehicle name</param>
    /// <param name="licensePlate">The license plate</param>
    /// <param name="status">The vehicle status</param>
    /// <param name="vehicleType">The vehicle type</param>
    /// <returns>A test vehicle</returns>
    public static Vehicle CreateVehicle(
        string? vehicleName = null,
        string? licensePlate = null,
        VehicleStatus status = VehicleStatus.Available,
        string? vehicleType = null)
    {
        var actualVehicleName = vehicleName ?? $"Vehicle {Faker.Random.AlphaNumeric(6)}";
        var actualLicensePlate = licensePlate ?? Faker.Random.Replace("???-####");
        var actualVehicleType = vehicleType ?? Faker.PickRandom("Truck", "Van", "Car", "SUV");
        var fuelType = Faker.PickRandom("Gasoline", "Diesel", "Electric", "Hybrid");

        var vehicle = new Vehicle(actualVehicleName, actualLicensePlate, actualVehicleType, fuelType);

        // Set status if different from default
        if (status != VehicleStatus.Available)
        {
            vehicle.UpdateStatus(status);
        }

        // Set random location
        var location = new Location(
            Faker.Address.Latitude(-90, 90),
            Faker.Address.Longitude(-180, 180)
        );
        vehicle.UpdateLocation(location);

        // Set random fuel level
        var fuelLevel = new FuelLevel(
            Faker.Random.Double(0, 100),
            100
        );
        vehicle.UpdateFuelLevel(fuelLevel);

        // Set random speed
        var speed = new Speed(
            Faker.Random.Double(0, 120),
            "mph"
        );
        vehicle.UpdateSpeed(speed);

        // Update basic info with additional details
        var brand = Faker.Vehicle.Manufacturer();
        var model = Faker.Vehicle.Model();
        var year = Faker.Random.Int(2015, 2024);
        var color = Faker.Commerce.Color();

        vehicle.UpdateBasicInfo(actualVehicleName, brand, model, year, color);

        return vehicle;
    }

    /// <summary>
    /// Creates a test driver with specified properties
    /// </summary>
    /// <param name="firstName">The first name</param>
    /// <param name="lastName">The last name</param>
    /// <param name="licenseNumber">The license number</param>
    /// <param name="status">The driver status</param>
    /// <returns>A test driver</returns>
    public static Driver CreateDriver(
        string? firstName = null,
        string? lastName = null,
        string? licenseNumber = null,
        DriverStatus status = DriverStatus.Active)
    {
        var actualFirstName = firstName ?? Faker.Name.FirstName();
        var actualLastName = lastName ?? Faker.Name.LastName();
        var actualLicenseNumber = licenseNumber ?? Faker.Random.Replace("D########");
        var phoneNumber = Faker.Phone.PhoneNumber();
        var email = Faker.Internet.Email(actualFirstName, actualLastName);

        var driver = new Driver(actualFirstName, actualLastName, email);

        // Set additional properties
        driver.UpdateLicenseInfo(actualLicenseNumber, "Class A");
        driver.UpdateContactInfo(phoneNumber, email);

        // Set status if different from default
        if (status != DriverStatus.Active)
        {
            driver.UpdateStatus(status);
        }

        return driver;
    }

    /// <summary>
    /// Creates a test alert with specified properties
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="type">The alert type</param>
    /// <param name="severity">The alert severity</param>
    /// <param name="status">The alert status</param>
    /// <returns>A test alert</returns>
    public static Alert CreateAlert(
        Guid vehicleId,
        AlertType type = AlertType.SpeedingViolation,
        AlertSeverity severity = AlertSeverity.Medium,
        AlertStatus status = AlertStatus.Active)
    {
        var message = type switch
        {
            AlertType.SpeedingViolation => "Vehicle exceeded speed limit",
            AlertType.FuelLow => "Vehicle fuel level is low",
            AlertType.MaintenanceDue => "Vehicle maintenance is due",
            AlertType.GeofenceViolation => "Vehicle left designated area",
            AlertType.HarshBraking => "Harsh braking detected",
            AlertType.HarshAcceleration => "Harsh acceleration detected",
            AlertType.IdlingExcessive => "Excessive idling detected",
            _ => "Alert triggered"
        };

        var description = $"{message} at {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC";

        var alert = new Alert(vehicleId, type, severity, message, description);
        
        if (status != AlertStatus.Active)
        {
            if (status == AlertStatus.Acknowledged)
            {
                alert.Acknowledge(Guid.NewGuid(), "Test acknowledgment");
            }
            else if (status == AlertStatus.Resolved)
            {
                alert.Acknowledge(Guid.NewGuid(), "Test acknowledgment");
                alert.Resolve(Guid.NewGuid(), "Test resolution");
            }
        }

        return alert;
    }

    /// <summary>
    /// Creates a test trip with specified properties
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="status">The trip status</param>
    /// <param name="startLocation">The start location</param>
    /// <param name="endLocation">The end location</param>
    /// <returns>A test trip</returns>
    public static Trip CreateTrip(
        Guid vehicleId,
        Guid driverId,
        TripStatus status = TripStatus.InProgress,
        string? startLocation = null,
        string? endLocation = null)
    {
        var actualStartLocation = startLocation ?? Faker.Address.FullAddress();
        var actualEndLocation = endLocation ?? Faker.Address.FullAddress();
        var startTime = Faker.Date.Recent(1);
        var plannedEndTime = startTime.AddHours(Faker.Random.Double(1, 8));

        var trip = new Trip(
            vehicleId,
            driverId,
            actualStartLocation,
            actualEndLocation,
            startTime,
            plannedEndTime
        );

        if (status == TripStatus.Completed)
        {
            var actualEndTime = startTime.AddHours(Faker.Random.Double(1, 6));
            var distance = Faker.Random.Double(10, 500);
            trip.Complete(actualEndTime, distance);
        }
        else if (status == TripStatus.Cancelled)
        {
            trip.Cancel("Test cancellation");
        }

        return trip;
    }

    /// <summary>
    /// Creates multiple test users with different roles
    /// </summary>
    /// <param name="count">The number of users to create</param>
    /// <returns>A collection of test users</returns>
    public static IEnumerable<User> CreateUsers(int count = 10)
    {
        var users = new List<User>();
        
        // Create at least one admin and one manager
        if (count > 0)
        {
            users.Add(CreateAdminUser());
        }
        
        if (count > 1)
        {
            users.Add(CreateManagerUser());
        }
        
        // Create remaining users as regular users
        for (int i = 2; i < count; i++)
        {
            users.Add(CreateUser());
        }

        return users;
    }

    /// <summary>
    /// Creates multiple test vehicles
    /// </summary>
    /// <param name="count">The number of vehicles to create</param>
    /// <returns>A collection of test vehicles</returns>
    public static IEnumerable<Vehicle> CreateVehicles(int count = 10)
    {
        return Enumerable.Range(0, count)
            .Select(_ => CreateVehicle())
            .ToList();
    }

    /// <summary>
    /// Creates multiple test drivers
    /// </summary>
    /// <param name="count">The number of drivers to create</param>
    /// <returns>A collection of test drivers</returns>
    public static IEnumerable<Driver> CreateDrivers(int count = 10)
    {
        return Enumerable.Range(0, count)
            .Select(_ => CreateDriver())
            .ToList();
    }
}
