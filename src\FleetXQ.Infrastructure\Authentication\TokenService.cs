using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace FleetXQ.Infrastructure.Authentication;

/// <summary>
/// Service for JWT token generation and validation
/// </summary>
public sealed class TokenService : ITokenService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenService> _logger;
    private readonly string _jwtKey;
    private readonly string _jwtIssuer;
    private readonly string _jwtAudience;
    private readonly TimeSpan _accessTokenExpiry;
    private readonly TimeSpan _refreshTokenExpiry;

    /// <summary>
    /// Initializes a new instance of the <see cref="TokenService"/> class
    /// </summary>
    /// <param name="configuration">The configuration</param>
    /// <param name="logger">The logger</param>
    public TokenService(IConfiguration configuration, ILogger<TokenService> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _jwtKey = _configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key not configured");
        _jwtIssuer = _configuration["Jwt:Issuer"] ?? throw new InvalidOperationException("JWT Issuer not configured");
        _jwtAudience = _configuration["Jwt:Audience"] ?? throw new InvalidOperationException("JWT Audience not configured");

        // Parse token expiry settings
        var accessTokenMinutes = _configuration.GetValue<int>("Jwt:ExpiryInMinutes", 60);
        var refreshTokenDays = _configuration.GetValue<int>("Jwt:RefreshTokenExpiryInDays", 7);

        _accessTokenExpiry = TimeSpan.FromMinutes(accessTokenMinutes);
        _refreshTokenExpiry = TimeSpan.FromDays(refreshTokenDays);

        _logger.LogInformation("TokenService initialized with access token expiry: {AccessTokenExpiry}, refresh token expiry: {RefreshTokenExpiry}",
            _accessTokenExpiry, _refreshTokenExpiry);
    }

    /// <summary>
    /// Generates an access token for a user
    /// </summary>
    /// <param name="user">The user</param>
    /// <returns>The access token</returns>
    public string GenerateAccessToken(User user)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_jwtKey);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new(ClaimTypes.Name, user.Username),
                new(ClaimTypes.Email, user.Email),
                new(ClaimTypes.GivenName, user.FirstName),
                new(ClaimTypes.Surname, user.LastName),
                new(ClaimTypes.Role, user.Role.ToString()),
                new("full_name", user.FullName),
                new("is_active", user.IsActive.ToString()),
                new("is_email_confirmed", user.IsEmailConfirmed.ToString()),
                new("jti", Guid.NewGuid().ToString()) // JWT ID for token tracking
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.Add(_accessTokenExpiry),
                Issuer = _jwtIssuer,
                Audience = _jwtAudience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            _logger.LogDebug("Generated access token for user {UserId} with expiry {Expiry}", user.Id, tokenDescriptor.Expires);

            return tokenString;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating access token for user {UserId}", user.Id);
            throw;
        }
    }

    /// <summary>
    /// Generates a refresh token
    /// </summary>
    /// <returns>The refresh token</returns>
    public string GenerateRefreshToken()
    {
        try
        {
            var randomNumber = new byte[64];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            var refreshToken = Convert.ToBase64String(randomNumber);

            _logger.LogDebug("Generated refresh token");

            return refreshToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating refresh token");
            throw;
        }
    }

    /// <summary>
    /// Validates a token
    /// </summary>
    /// <param name="token">The token to validate</param>
    /// <returns>True if the token is valid</returns>
    public bool ValidateToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return false;

        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_jwtKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = _jwtIssuer,
                ValidAudience = _jwtAudience,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ClockSkew = TimeSpan.Zero
            };

            tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

            _logger.LogDebug("Token validation successful");
            return true;
        }
        catch (SecurityTokenExpiredException)
        {
            _logger.LogDebug("Token validation failed - token expired");
            return false;
        }
        catch (SecurityTokenException ex)
        {
            _logger.LogDebug("Token validation failed - {Error}", ex.Message);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during token validation");
            return false;
        }
    }

    /// <summary>
    /// Gets the user ID from a token
    /// </summary>
    /// <param name="token">The token</param>
    /// <returns>The user ID if valid, null otherwise</returns>
    public Guid? GetUserIdFromToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return null;

        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_jwtKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = false, // Allow expired tokens for refresh scenarios
                ValidateIssuerSigningKey = true,
                ValidIssuer = _jwtIssuer,
                ValidAudience = _jwtAudience,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
            var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier);

            if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
            {
                _logger.LogDebug("Successfully extracted user ID {UserId} from token", userId);
                return userId;
            }

            _logger.LogDebug("Failed to extract user ID from token - claim not found or invalid format");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Error extracting user ID from token: {Error}", ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Gets the token expiry time
    /// </summary>
    /// <param name="token">The token</param>
    /// <returns>The expiry time if valid, null otherwise</returns>
    public DateTime? GetTokenExpiry(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return null;

        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);

            var expiry = jsonToken.ValidTo;
            _logger.LogDebug("Token expiry: {Expiry}", expiry);

            return expiry;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Error getting token expiry: {Error}", ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Gets the default access token expiry duration
    /// </summary>
    /// <returns>The expiry duration</returns>
    public TimeSpan GetAccessTokenExpiry()
    {
        return _accessTokenExpiry;
    }

    /// <summary>
    /// Gets the default refresh token expiry duration
    /// </summary>
    /// <returns>The expiry duration</returns>
    public TimeSpan GetRefreshTokenExpiry()
    {
        return _refreshTokenExpiry;
    }

    /// <summary>
    /// Validates a refresh token format
    /// </summary>
    /// <param name="refreshToken">The refresh token to validate</param>
    /// <returns>True if the format is valid</returns>
    public bool IsValidRefreshTokenFormat(string refreshToken)
    {
        if (string.IsNullOrWhiteSpace(refreshToken))
            return false;

        try
        {
            // Refresh tokens should be base64 encoded and have a minimum length
            var bytes = Convert.FromBase64String(refreshToken);
            return bytes.Length >= 32; // Minimum 32 bytes for security
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Generates a secure refresh token with additional entropy
    /// </summary>
    /// <param name="userId">The user ID for additional entropy</param>
    /// <returns>The refresh token</returns>
    public string GenerateSecureRefreshToken(Guid userId)
    {
        try
        {
            var randomBytes = new byte[64];
            var userIdBytes = userId.ToByteArray();
            var timestampBytes = BitConverter.GetBytes(DateTimeOffset.UtcNow.ToUnixTimeSeconds());

            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomBytes);

            // Combine random bytes with user ID and timestamp for additional entropy
            var combinedBytes = new byte[randomBytes.Length + userIdBytes.Length + timestampBytes.Length];
            Buffer.BlockCopy(randomBytes, 0, combinedBytes, 0, randomBytes.Length);
            Buffer.BlockCopy(userIdBytes, 0, combinedBytes, randomBytes.Length, userIdBytes.Length);
            Buffer.BlockCopy(timestampBytes, 0, combinedBytes, randomBytes.Length + userIdBytes.Length, timestampBytes.Length);

            var refreshToken = Convert.ToBase64String(combinedBytes);

            _logger.LogDebug("Generated secure refresh token for user {UserId}", userId);

            return refreshToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating secure refresh token for user {UserId}", userId);
            throw;
        }
    }
}
