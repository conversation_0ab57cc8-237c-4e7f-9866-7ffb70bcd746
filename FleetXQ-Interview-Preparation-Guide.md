# Fleet XQ Technical Interview - Comprehensive Preparation Guide

## 🎯 Overview

This guide provides comprehensive preparation for your Fleet XQ live coding interview, including ready-to-use code snippets, AI prompts, and step-by-step processes for each development phase.

## 📋 Quick Reference Checklist

### Pre-Interview Setup
- [ ] .NET 8 SDK installed and verified
- [ ] Visual Studio 2022 or VS Code with C# extension
- [ ] SQL Server Express + SSMS ready
- [ ] AI tools (Cursor, ChatGPT, Claude) logged in and tested
- [ ] This preparation guide accessible during interview

### Key Technologies Stack
- **Backend**: .NET 8, Entity Framework Core, MediatR, FluentValidation
- **Database**: SQL Server, Database-First EF Core approach
- **Architecture**: Clean Architecture with CQRS pattern
- **Testing**: xUnit, Moq, FluentAssertions
- **Frontend**: React/Vue/Angular (your choice)
- **Real-time**: SignalR for live updates

## 🏗️ Phase 1: Database Foundation (20 min)

### Key Concepts to Remember
- Complex JOINs and aggregations
- Window functions for time-series data
- Fleet management specific queries (vehicle status, telemetry analysis)
- Performance optimization techniques

### Common SQL Patterns for Fleet Management

#### Vehicle Status Analysis
```sql
-- Get vehicle status distribution
SELECT 
    Status,
    COUNT(*) as VehicleCount,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as Percentage
FROM Vehicles 
GROUP BY Status;

-- Latest telemetry per vehicle with window function
SELECT 
    v.VehicleId,
    v.LicensePlate,
    t.Speed,
    t.FuelLevel,
    t.Timestamp,
    ROW_NUMBER() OVER (PARTITION BY v.VehicleId ORDER BY t.Timestamp DESC) as rn
FROM Vehicles v
LEFT JOIN TelemetryData t ON v.VehicleId = t.VehicleId
WHERE ROW_NUMBER() OVER (PARTITION BY v.VehicleId ORDER BY t.Timestamp DESC) = 1;
```

#### Time-Series Analysis
```sql
-- Average speed by hour for last 24 hours
SELECT 
    DATEPART(HOUR, Timestamp) as Hour,
    AVG(Speed) as AvgSpeed,
    COUNT(*) as ReadingCount
FROM TelemetryData 
WHERE Timestamp >= DATEADD(HOUR, -24, GETUTCDATE())
GROUP BY DATEPART(HOUR, Timestamp)
ORDER BY Hour;
```

### AI Prompt for SQL Generation
```
Generate a SQL query for fleet management that:
- Analyzes [specific metric like fuel consumption, speed patterns, etc.]
- Uses [specific time period like last 7 days, current month]
- Includes [specific grouping like by vehicle, by driver, by route]
- Returns results optimized for [dashboard display, reporting, alerts]

Database schema includes: Vehicles, Drivers, TelemetryData, Alerts, Routes tables.
Use SQL Server syntax with proper indexing considerations.
```

## 🔧 Phase 2: Core API Development (25 min)

### Clean Architecture Project Structure
```
/src
  /FleetXQ.Api          - Controllers, SignalR hubs, middleware
  /FleetXQ.Application  - CQRS handlers, DTOs, validation
  /FleetXQ.Domain       - Entities, value objects, interfaces
  /FleetXQ.Infrastructure - EF Core, repositories, external services
/tests
  /FleetXQ.Application.Tests - Unit tests with base classes
```

### CQRS Command Pattern Template
```csharp
// Command
public record CreateVehicleCommand(
    string Name,
    string LicensePlate,
    string VehicleType) : IRequest<Result<VehicleDto>>;

// Command Handler
public class CreateVehicleCommandHandler : IRequestHandler<CreateVehicleCommand, Result<VehicleDto>>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateVehicleCommandHandler> _logger;

    public CreateVehicleCommandHandler(
        IVehicleRepository vehicleRepository,
        IMapper mapper,
        ILogger<CreateVehicleCommandHandler> logger)
    {
        _vehicleRepository = vehicleRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<VehicleDto>> Handle(CreateVehicleCommand request, CancellationToken cancellationToken)
    {
        // 1. Validation (handled by FluentValidation pipeline)
        // 2. Business logic
        var vehicle = new Vehicle(request.Name, request.LicensePlate, request.VehicleType);
        
        // 3. Persistence
        await _vehicleRepository.AddAsync(vehicle, cancellationToken);
        
        // 4. Mapping and return
        var dto = _mapper.Map<VehicleDto>(vehicle);
        return Result<VehicleDto>.Success(dto);
    }
}

// Validator
public class CreateVehicleCommandValidator : AbstractValidator<CreateVehicleCommand>
{
    public CreateVehicleCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100);
            
        RuleFor(x => x.LicensePlate)
            .NotEmpty()
            .Matches(@"^[A-Z0-9-]+$");
    }
}
```

### CQRS Query Pattern Template
```csharp
// Query
public record GetVehicleByIdQuery(Guid VehicleId) : IRequest<Result<VehicleDto>>;

// Query Handler
public class GetVehicleByIdQueryHandler : IRequestHandler<GetVehicleByIdQuery, Result<VehicleDto>>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleByIdQueryHandler> _logger;

    public async Task<Result<VehicleDto>> Handle(GetVehicleByIdQuery request, CancellationToken cancellationToken)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
        
        if (vehicle == null)
            return Result<VehicleDto>.Failure("Vehicle not found");
            
        var dto = _mapper.Map<VehicleDto>(vehicle);
        return Result<VehicleDto>.Success(dto);
    }
}
```

### Complete Service-to-Controller Wiring Examples

#### 1. Dependency Injection Setup (Program.cs)
```csharp
// Program.cs - Complete DI Configuration
var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Database Configuration
builder.Services.AddDbContext<FleetXQDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// MediatR Configuration
builder.Services.AddMediatR(cfg => {
    cfg.RegisterServicesFromAssembly(typeof(CreateVehicleCommandHandler).Assembly);
});

// AutoMapper Configuration
builder.Services.AddAutoMapper(typeof(MappingProfile));

// FluentValidation Configuration
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssembly(typeof(CreateVehicleCommandValidator).Assembly);

// Repository Registration
builder.Services.AddScoped<IVehicleRepository, VehicleRepository>();
builder.Services.AddScoped<IDriverRepository, DriverRepository>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IAlertRepository, AlertRepository>();

// Service Registration
builder.Services.AddScoped<IPasswordHashingService, PasswordHashingService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<IAlertEvaluationService, AlertEvaluationService>();

// Authentication & Authorization
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

// CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
        policy.WithOrigins("http://localhost:3000")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials());
});

// SignalR Configuration
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowReactApp");
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapHub<TelemetryHub>("/telemetryHub");

app.Run();
```

#### 2. Base Controller with MediatR Integration
```csharp
// BaseApiController.cs
using MediatR;
using Microsoft.AspNetCore.Mvc;

[ApiController]
public abstract class BaseApiController : ControllerBase
{
    private ISender _mediator = null!;

    protected ISender Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<ISender>();

    protected ActionResult<ApiResponse<T>> HandleResult<T>(Result<T> result)
    {
        if (result.IsSuccess)
            return Ok(ApiResponse<T>.Success(result.Value));

        return result.Error switch
        {
            "NotFound" => NotFound(ApiResponse<T>.Failure(result.Error)),
            "Unauthorized" => Unauthorized(ApiResponse<T>.Failure(result.Error)),
            "Forbidden" => Forbid(),
            _ => BadRequest(ApiResponse<T>.Failure(result.Error))
        };
    }

    protected ActionResult<PaginatedApiResponse<T>> HandlePaginatedResult<T>(PaginatedResult<T> result)
    {
        if (result.IsSuccess)
            return Ok(PaginatedApiResponse<T>.Success(
                result.Value,
                result.PageNumber,
                result.PageSize,
                result.TotalCount));

        return BadRequest(PaginatedApiResponse<T>.Failure(result.Error));
    }
}
```

#### 3. Complete Controller Implementation
```csharp
// VehiclesController.cs - Full Implementation
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class VehiclesController : BaseApiController
{
    /// <summary>
    /// Creates a new vehicle
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> CreateVehicle(CreateVehicleCommand command)
    {
        var result = await Mediator.Send(command);

        if (result.IsSuccess)
            return CreatedAtAction(nameof(GetVehicle), new { id = result.Value.Id },
                ApiResponse<VehicleDto>.Success(result.Value));

        return HandleResult(result);
    }

    /// <summary>
    /// Gets a vehicle by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> GetVehicle(Guid id)
    {
        var result = await Mediator.Send(new GetVehicleByIdQuery(id));
        return HandleResult(result);
    }

    /// <summary>
    /// Gets all vehicles with pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(PaginatedApiResponse<VehicleListDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedApiResponse<VehicleListDto>>> GetVehicles(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] VehicleStatus? status = null)
    {
        var query = new GetVehicleListQuery(pageNumber, pageSize, searchTerm, status);
        var result = await Mediator.Send(query);
        return HandlePaginatedResult(result);
    }

    /// <summary>
    /// Updates a vehicle
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,Manager")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> UpdateVehicle(Guid id, UpdateVehicleCommand command)
    {
        if (id != command.Id)
            return BadRequest(ApiResponse<VehicleDto>.Failure("ID mismatch"));

        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Updates vehicle status
    /// </summary>
    [HttpPut("{id:guid}/status")]
    [Authorize(Roles = "Admin,Manager,Driver")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> UpdateVehicleStatus(
        Guid id,
        [FromBody] UpdateVehicleStatusRequest request)
    {
        var command = new UpdateVehicleStatusCommand(id, request.Status, request.Notes);
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Deletes a vehicle
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteVehicle(Guid id)
    {
        var result = await Mediator.Send(new DeleteVehicleCommand(id));

        if (result.IsSuccess)
            return NoContent();

        return HandleResult(Result<object>.Failure(result.Error));
    }
}
```

#### 4. Service Layer Integration Examples

##### Authentication Controller with Service Dependencies
```csharp
// AuthController.cs - Complete Service Integration
[ApiController]
[Route("api/[controller]")]
public class AuthController : BaseApiController
{
    /// <summary>
    /// User login endpoint
    /// </summary>
    [HttpPost("login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<LoginResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ApiResponse<LoginResponseDto>>> Login(LoginUserCommand command)
    {
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Token refresh endpoint
    /// </summary>
    [HttpPost("refresh")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<TokenResponseDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<TokenResponseDto>>> RefreshToken(RefreshTokenCommand command)
    {
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// User logout endpoint
    /// </summary>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult> Logout()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userId, out var userGuid))
        {
            await Mediator.Send(new LogoutUserCommand(userGuid));
        }
        return NoContent();
    }

    /// <summary>
    /// Get current user profile
    /// </summary>
    [HttpGet("profile")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<UserProfileDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetProfile()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (!Guid.TryParse(userId, out var userGuid))
            return Unauthorized(ApiResponse<UserProfileDto>.Failure("Invalid user token"));

        var result = await Mediator.Send(new GetUserProfileQuery(userGuid));
        return HandleResult(result);
    }
}
```

##### Telemetry Controller with Real-time Integration
```csharp
// TelemetryController.cs - SignalR Integration
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TelemetryController : BaseApiController
{
    /// <summary>
    /// Process telemetry data batch
    /// </summary>
    [HttpPost("batch")]
    [Authorize(Roles = "Admin,System")]
    [ProducesResponseType(typeof(ApiResponse<BatchProcessResultDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<BatchProcessResultDto>>> ProcessTelemetryBatch(
        ProcessTelemetryBatchCommand command)
    {
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Get latest telemetry for a vehicle
    /// </summary>
    [HttpGet("{vehicleId:guid}/latest")]
    [ProducesResponseType(typeof(ApiResponse<TelemetryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<TelemetryDto>>> GetLatestTelemetry(Guid vehicleId)
    {
        var result = await Mediator.Send(new GetLatestTelemetryQuery(vehicleId));
        return HandleResult(result);
    }

    /// <summary>
    /// Get telemetry history with pagination
    /// </summary>
    [HttpGet("{vehicleId:guid}/history")]
    [ProducesResponseType(typeof(PaginatedApiResponse<TelemetryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedApiResponse<TelemetryDto>>> GetTelemetryHistory(
        Guid vehicleId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        var query = new GetTelemetryHistoryQuery(
            vehicleId,
            startDate ?? DateTime.UtcNow.AddDays(-1),
            endDate ?? DateTime.UtcNow,
            pageNumber,
            pageSize);

        var result = await Mediator.Send(query);
        return HandlePaginatedResult(result);
    }
}
```

#### 5. SignalR Hub Integration
```csharp
// TelemetryHub.cs - Real-time Communication
[Authorize]
public class TelemetryHub : Hub
{
    private readonly ILogger<TelemetryHub> _logger;
    private readonly IVehicleRepository _vehicleRepository;

    public TelemetryHub(ILogger<TelemetryHub> logger, IVehicleRepository vehicleRepository)
    {
        _logger = logger;
        _vehicleRepository = vehicleRepository;
    }

    /// <summary>
    /// Subscribe to vehicle updates
    /// </summary>
    public async Task SubscribeToVehicle(Guid vehicleId)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(vehicleId);
        if (vehicle == null)
        {
            await Clients.Caller.SendAsync("Error", "Vehicle not found");
            return;
        }

        await Groups.AddToGroupAsync(Context.ConnectionId, $"Vehicle_{vehicleId}");
        _logger.LogInformation("User {UserId} subscribed to vehicle {VehicleId}",
            Context.UserIdentifier, vehicleId);

        await Clients.Caller.SendAsync("SubscriptionConfirmed", vehicleId);
    }

    /// <summary>
    /// Unsubscribe from vehicle updates
    /// </summary>
    public async Task UnsubscribeFromVehicle(Guid vehicleId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Vehicle_{vehicleId}");
        _logger.LogInformation("User {UserId} unsubscribed from vehicle {VehicleId}",
            Context.UserIdentifier, vehicleId);
    }

    /// <summary>
    /// Subscribe to all alerts
    /// </summary>
    public async Task SubscribeToAlerts()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "Alerts");
        await Clients.Caller.SendAsync("AlertSubscriptionConfirmed");
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("User {UserId} connected to telemetry hub", Context.UserIdentifier);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("User {UserId} disconnected from telemetry hub", Context.UserIdentifier);
        await base.OnDisconnectedAsync(exception);
    }
}
```

#### 6. Domain Event Handler Integration
```csharp
// TelemetryDataReceivedEventHandler.cs - Domain Event to SignalR
public class TelemetryDataReceivedEventHandler : INotificationHandler<TelemetryDataReceivedEvent>
{
    private readonly IHubContext<TelemetryHub> _hubContext;
    private readonly IMapper _mapper;
    private readonly ILogger<TelemetryDataReceivedEventHandler> _logger;

    public TelemetryDataReceivedEventHandler(
        IHubContext<TelemetryHub> hubContext,
        IMapper mapper,
        ILogger<TelemetryDataReceivedEventHandler> logger)
    {
        _hubContext = hubContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task Handle(TelemetryDataReceivedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var telemetryDto = _mapper.Map<TelemetryDto>(notification.TelemetryData);

            // Send to vehicle-specific group
            await _hubContext.Clients.Group($"Vehicle_{notification.TelemetryData.VehicleId}")
                .SendAsync("TelemetryUpdate", telemetryDto, cancellationToken);

            // Send to dashboard group if needed
            await _hubContext.Clients.Group("Dashboard")
                .SendAsync("VehicleStatusUpdate", new
                {
                    VehicleId = notification.TelemetryData.VehicleId,
                    LastUpdate = notification.TelemetryData.Timestamp,
                    Status = "Active"
                }, cancellationToken);

            _logger.LogDebug("Telemetry update sent for vehicle {VehicleId}",
                notification.TelemetryData.VehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending telemetry update for vehicle {VehicleId}",
                notification.TelemetryData.VehicleId);
        }
    }
}
```

#### 7. Middleware Integration Examples

##### Global Exception Handling Middleware
```csharp
// GlobalExceptionMiddleware.cs
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = exception switch
        {
            ValidationException validationEx => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status400BadRequest,
                Message = "Validation failed",
                Errors = validationEx.Errors.Select(e => e.ErrorMessage).ToList()
            },
            UnauthorizedAccessException => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status401Unauthorized,
                Message = "Unauthorized access"
            },
            NotFoundException => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status404NotFound,
                Message = "Resource not found"
            },
            _ => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status500InternalServerError,
                Message = "An internal server error occurred"
            }
        };

        context.Response.StatusCode = response.StatusCode;
        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}

// Extension method for middleware registration
public static class MiddlewareExtensions
{
    public static IApplicationBuilder UseGlobalExceptionHandling(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<GlobalExceptionMiddleware>();
    }
}
```

##### Request Logging Middleware
```csharp
// RequestLoggingMiddleware.cs
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();

        _logger.LogInformation("Request started: {Method} {Path} from {RemoteIpAddress}",
            context.Request.Method,
            context.Request.Path,
            context.Connection.RemoteIpAddress);

        await _next(context);

        stopwatch.Stop();

        _logger.LogInformation("Request completed: {Method} {Path} responded {StatusCode} in {ElapsedMilliseconds}ms",
            context.Request.Method,
            context.Request.Path,
            context.Response.StatusCode,
            stopwatch.ElapsedMilliseconds);
    }
}
```

#### 8. Complete Startup Configuration with All Wiring
```csharp
// Program.cs - Complete Production-Ready Configuration
var builder = WebApplication.CreateBuilder(args);

// Logging Configuration
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Add services to the container
builder.Services.AddControllers(options =>
{
    options.Filters.Add<ValidationFilter>();
    options.SuppressAsyncSuffixInActionNames = false;
});

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "FleetXQ API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Database Configuration
builder.Services.AddDbContext<FleetXQDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
    options.EnableSensitiveDataLogging(builder.Environment.IsDevelopment());
    options.EnableDetailedErrors(builder.Environment.IsDevelopment());
});

// MediatR Configuration with Pipeline Behaviors
builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssembly(typeof(CreateVehicleCommandHandler).Assembly);
    cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
    cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
    cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));
});

// AutoMapper Configuration
builder.Services.AddAutoMapper(typeof(MappingProfile));

// FluentValidation Configuration
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssembly(typeof(CreateVehicleCommandValidator).Assembly);

// Repository Registration
builder.Services.AddScoped<IVehicleRepository, VehicleRepository>();
builder.Services.AddScoped<IDriverRepository, DriverRepository>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IAlertRepository, AlertRepository>();
builder.Services.AddScoped<ITelemetryRepository, TelemetryRepository>();

// Service Registration
builder.Services.AddScoped<IPasswordHashingService, PasswordHashingService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<IAlertEvaluationService, AlertEvaluationService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<ISmsService, SmsService>();

// Authentication & Authorization
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"])),
            ClockSkew = TimeSpan.Zero
        };

        // Configure SignalR authentication
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                var accessToken = context.Request.Query["access_token"];
                var path = context.HttpContext.Request.Path;

                if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/telemetryHub"))
                {
                    context.Token = accessToken;
                }
                return Task.CompletedTask;
            }
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
    options.AddPolicy("ManagerOrAdmin", policy => policy.RequireRole("Admin", "Manager"));
    options.AddPolicy("DriverAccess", policy => policy.RequireRole("Admin", "Manager", "Driver"));
});

// CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
        policy.WithOrigins(builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:3000" })
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials());
});

// SignalR Configuration
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
});

// Health Checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<FleetXQDbContext>()
    .AddCheck("SignalR", () => HealthCheckResult.Healthy());

// Memory Cache
builder.Services.AddMemoryCache();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "FleetXQ API V1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}

// Middleware Pipeline Order is Important!
app.UseGlobalExceptionHandling();
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseHttpsRedirection();
app.UseCors("AllowReactApp");
app.UseAuthentication();
app.UseAuthorization();

// Map endpoints
app.MapControllers();
app.MapHub<TelemetryHub>("/telemetryHub");
app.MapHub<AlertHub>("/alertHub");
app.MapHealthChecks("/health");

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<FleetXQDbContext>();
    context.Database.EnsureCreated();
}

app.Run();
```

#### 9. API Response Models and Validation
```csharp
// ApiResponse.cs - Standardized API Response
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public int StatusCode { get; set; }
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static ApiResponse<T> Success(T data, string message = "Success")
    {
        return new ApiResponse<T>
        {
            Success = true,
            StatusCode = 200,
            Message = message,
            Data = data
        };
    }

    public static ApiResponse<T> Failure(string message, int statusCode = 400, List<string>? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            StatusCode = statusCode,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}

// PaginatedApiResponse.cs - For paginated results
public class PaginatedApiResponse<T> : ApiResponse<IEnumerable<T>>
{
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;

    public static PaginatedApiResponse<T> Success(
        IEnumerable<T> data,
        int pageNumber,
        int pageSize,
        int totalCount,
        string message = "Success")
    {
        return new PaginatedApiResponse<T>
        {
            Success = true,
            StatusCode = 200,
            Message = message,
            Data = data,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalCount = totalCount
        };
    }

    public static PaginatedApiResponse<T> Failure(string message, int statusCode = 400)
    {
        return new PaginatedApiResponse<T>
        {
            Success = false,
            StatusCode = statusCode,
            Message = message,
            Data = Enumerable.Empty<T>()
        };
    }
}
```

#### 10. Validation Filter and Action Filters
```csharp
// ValidationFilter.cs - Global Model Validation
public class ValidationFilter : IActionFilter
{
    public void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
        {
            var errors = context.ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .SelectMany(x => x.Value!.Errors)
                .Select(x => x.ErrorMessage)
                .ToList();

            var response = ApiResponse<object>.Failure("Validation failed", 400, errors);
            context.Result = new BadRequestObjectResult(response);
        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // No implementation needed
    }
}

// LoggingActionFilter.cs - Action-level logging
public class LoggingActionFilter : IActionFilter
{
    private readonly ILogger<LoggingActionFilter> _logger;

    public LoggingActionFilter(ILogger<LoggingActionFilter> logger)
    {
        _logger = logger;
    }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        var actionName = context.ActionDescriptor.DisplayName;
        var controllerName = context.Controller.GetType().Name;

        _logger.LogInformation("Executing action {ActionName} in {ControllerName}",
            actionName, controllerName);
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        var actionName = context.ActionDescriptor.DisplayName;
        var statusCode = context.HttpContext.Response.StatusCode;

        _logger.LogInformation("Action {ActionName} completed with status code {StatusCode}",
            actionName, statusCode);
    }
}
```

#### 11. Request/Response DTOs with Validation
```csharp
// UpdateVehicleStatusRequest.cs - Request DTO
public class UpdateVehicleStatusRequest
{
    [Required]
    [EnumDataType(typeof(VehicleStatus))]
    public VehicleStatus Status { get; set; }

    [MaxLength(500)]
    public string? Notes { get; set; }
}

// VehicleDto.cs - Response DTO
public class VehicleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string LicensePlate { get; set; } = string.Empty;
    public string VehicleType { get; set; } = string.Empty;
    public VehicleStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdated { get; set; }
    public DriverDto? AssignedDriver { get; set; }
    public TelemetryDto? LatestTelemetry { get; set; }
}

// VehicleListDto.cs - Simplified DTO for lists
public class VehicleListDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string LicensePlate { get; set; } = string.Empty;
    public VehicleStatus Status { get; set; }
    public string? DriverName { get; set; }
    public DateTime? LastTelemetryUpdate { get; set; }
}
```

#### 12. Complete AI Prompt for Service-to-Controller Wiring
```
Generate complete service-to-controller wiring for FleetXQ feature:

Feature: [Feature name like Vehicle Management, Driver Assignment, etc.]
Operations: [List of CRUD and custom operations needed]

Requirements:
1. Complete DI registration in Program.cs
2. Controller with all endpoints and proper HTTP verbs
3. Request/Response DTOs with validation attributes
4. Proper error handling with standardized ApiResponse
5. Authorization attributes based on user roles
6. SignalR integration if real-time updates needed
7. Action filters for logging and validation
8. Swagger documentation attributes
9. Proper async/await patterns
10. Integration with MediatR for CQRS

Generate:
- DI registration code for Program.cs
- Complete controller class with all endpoints
- Request/Response DTO classes
- Any required middleware or filters
- SignalR hub methods if applicable

Follow Clean Architecture patterns and existing codebase conventions.
```

### AI Prompts for Backend Development

#### Generate CQRS Command/Query
```
Create a CQRS [command/query] for FleetXQ fleet management system:

Feature: [Vehicle Management, Driver Assignment, Telemetry Processing, Alert Management]
Operation: [Create, Update, Delete, Get, List, Process]
Entity: [Vehicle, Driver, TelemetryData, Alert]

Requirements:
- Follow Clean Architecture patterns
- Use MediatR for request handling
- Include FluentValidation for commands
- Use AutoMapper for DTO mapping
- Include proper error handling with Result pattern
- Add logging with ILogger
- Follow existing codebase conventions

Generate: Command/Query class, Handler, Validator (if command), and DTO classes.
```

#### Generate Repository Interface and Implementation
```
Create repository interface and EF Core implementation for [Entity Name] in FleetXQ:

Requirements:
- Follow repository pattern from existing codebase
- Include standard CRUD operations
- Add domain-specific query methods for [specific operations]
- Use async/await pattern
- Include proper cancellation token support
- Follow database-first EF Core approach
- Include proper error handling

Entity context: [Brief description of the entity and its relationships]
```

## 🧪 Comprehensive Unit Testing Strategy

### Test Base Classes Structure
```csharp
// Application Test Base
public abstract class TestBase
{
    protected readonly IMapper Mapper;
    protected readonly Mock<IVehicleRepository> MockVehicleRepository;
    protected readonly Mock<IUserRepository> MockUserRepository;
    // ... other repository mocks
    
    protected TestBase()
    {
        // Setup AutoMapper with production profiles
        var mapperConfig = new MapperConfiguration(cfg => cfg.AddProfile<MappingProfile>());
        Mapper = mapperConfig.CreateMapper();
        
        // Initialize all repository mocks
        MockVehicleRepository = new Mock<IVehicleRepository>();
        // ... setup other mocks
    }
}

// Command Handler Test Base
public abstract class CommandHandlerTestBase<THandler> : TestBase where THandler : class
{
    protected readonly Mock<ILogger<THandler>> MockLogger;
    
    protected CommandHandlerTestBase()
    {
        MockLogger = CreateMockLogger<THandler>();
    }
    
    protected void VerifyRepositoryAddCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository)
        where TEntity : class
    {
        mockRepository.Verify(x => x.AddAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()), Times.Once);
    }
}
```

### Complete Test Template
```csharp
public class CreateVehicleCommandHandlerTests : CommandHandlerTestBase<CreateVehicleCommandHandler>
{
    private readonly CreateVehicleCommandHandler _handler;

    public CreateVehicleCommandHandlerTests()
    {
        _handler = new CreateVehicleCommandHandler(
            MockVehicleRepository.Object,
            Mapper,
            MockLogger.Object);
    }

    [Fact]
    public async Task Handle_ValidCommand_ShouldCreateVehicleSuccessfully()
    {
        // Arrange
        var command = new CreateVehicleCommand("Test Vehicle", "ABC-123", "Truck");
        var cancellationToken = new CancellationToken();

        MockVehicleRepository
            .Setup(x => x.AddAsync(It.IsAny<Vehicle>(), cancellationToken))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
        result.Value.Name.Should().Be("Test Vehicle");
        result.Value.LicensePlate.Should().Be("ABC-123");

        VerifyRepositoryAddCalled(MockVehicleRepository);
        VerifyInformationLogged();
    }

    [Fact]
    public async Task Handle_RepositoryThrowsException_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateVehicleCommand("Test Vehicle", "ABC-123", "Truck");
        var cancellationToken = new CancellationToken();

        MockVehicleRepository
            .Setup(x => x.AddAsync(It.IsAny<Vehicle>(), cancellationToken))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, cancellationToken));

        VerifyErrorLogged();
    }
}
```

### AI Prompt for Test Generation
```
Generate comprehensive unit tests for [CommandHandler/QueryHandler] in FleetXQ using the established testing patterns:

Handler: [Handler class name]
Feature: [Brief description of what the handler does]

Requirements:
- Use CommandHandlerTestBase<T> or QueryHandlerTestBase<T> as base class
- Include these test scenarios:
  * Happy path with valid input
  * Validation failures (if applicable)
  * Business rule violations
  * Repository exceptions
  * Edge cases specific to the feature
- Use FluentAssertions for assertions
- Mock all dependencies using Moq
- Follow AAA pattern (Arrange, Act, Assert)
- Include verification of repository calls and logging
- Use existing test patterns from the codebase

Generate complete test class with all scenarios.
```

## 🎨 Phase 4: Frontend Integration (30 min)

### React Component Structure
```jsx
// Vehicle Management Component
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchVehicles, createVehicle } from '../store/vehicleSlice';

const VehicleManagement = () => {
    const dispatch = useDispatch();
    const { vehicles, loading, error } = useSelector(state => state.vehicles);
    const [formData, setFormData] = useState({
        name: '',
        licensePlate: '',
        vehicleType: ''
    });

    useEffect(() => {
        dispatch(fetchVehicles());
    }, [dispatch]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            await dispatch(createVehicle(formData)).unwrap();
            setFormData({ name: '', licensePlate: '', vehicleType: '' });
        } catch (error) {
            console.error('Failed to create vehicle:', error);
        }
    };

    return (
        <div className="vehicle-management">
            <form onSubmit={handleSubmit}>
                <input
                    type="text"
                    placeholder="Vehicle Name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                />
                {/* Additional form fields */}
                <button type="submit" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Vehicle'}
                </button>
            </form>
            
            <div className="vehicle-list">
                {vehicles.map(vehicle => (
                    <div key={vehicle.id} className="vehicle-card">
                        <h3>{vehicle.name}</h3>
                        <p>License: {vehicle.licensePlate}</p>
                        <p>Status: {vehicle.status}</p>
                    </div>
                ))}
            </div>
        </div>
    );
};
```

### API Service Layer
```javascript
// vehicleService.js
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:7001/api';

const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

export const vehicleService = {
    getAll: () => apiClient.get('/vehicles'),
    getById: (id) => apiClient.get(`/vehicles/${id}`),
    create: (vehicle) => apiClient.post('/vehicles', vehicle),
    update: (id, vehicle) => apiClient.put(`/vehicles/${id}`, vehicle),
    delete: (id) => apiClient.delete(`/vehicles/${id}`),
};
```

### AI Prompt for Frontend Development
```
Create a React component for FleetXQ fleet management:

Component: [Component name and purpose]
Features needed:
- [List specific features like CRUD operations, real-time updates, etc.]
- API integration with backend endpoints
- Form handling with validation
- Loading and error states
- Responsive design

Requirements:
- Use functional components with hooks
- Implement Redux Toolkit for state management
- Include proper error handling
- Add loading indicators
- Use modern JavaScript (ES6+)
- Follow React best practices
- Include TypeScript types if applicable

API endpoints available: [List relevant endpoints]
```

## 📊 Phase 5: Data Visualization (25 min)

### Chart.js Integration
```javascript
// TelemetryChart.jsx
import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

const TelemetryChart = ({ vehicleId, timeRange = '24h' }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        const fetchTelemetryData = async () => {
            try {
                const response = await fetch(`/api/telemetry/${vehicleId}/history?range=${timeRange}`);
                const data = await response.json();
                
                if (chartInstance.current) {
                    chartInstance.current.destroy();
                }

                const ctx = chartRef.current.getContext('2d');
                chartInstance.current = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.timestamps,
                        datasets: [
                            {
                                label: 'Speed (km/h)',
                                data: data.speeds,
                                borderColor: 'rgb(75, 192, 192)',
                                tension: 0.1
                            },
                            {
                                label: 'Fuel Level (%)',
                                data: data.fuelLevels,
                                borderColor: 'rgb(255, 99, 132)',
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Failed to fetch telemetry data:', error);
            }
        };

        fetchTelemetryData();
        
        return () => {
            if (chartInstance.current) {
                chartInstance.current.destroy();
            }
        };
    }, [vehicleId, timeRange]);

    return <canvas ref={chartRef} />;
};
```

### AI Prompt for Data Visualization
```
Create a data visualization component for FleetXQ dashboard:

Chart type: [Line chart, Bar chart, Pie chart, etc.]
Data source: [Telemetry data, Vehicle status, Alert trends, etc.]
Features needed:
- Real-time updates every [time interval]
- Interactive tooltips and legends
- Time range selection (1h, 24h, 7d, 30d)
- Export functionality
- Responsive design

Requirements:
- Use Chart.js or D3.js
- Implement proper data fetching with error handling
- Add loading states
- Include chart configuration options
- Support multiple data series
- Optimize for performance with large datasets

Data format: [Describe the expected data structure from API]
```

## 🚀 Advanced Features & Optimization

### SignalR Real-time Integration
```javascript
// signalRService.js
import * as signalR from '@microsoft/signalr';

class SignalRService {
    constructor() {
        this.connection = null;
    }

    async startConnection() {
        this.connection = new signalR.HubConnectionBuilder()
            .withUrl('/telemetryHub', {
                accessTokenFactory: () => localStorage.getItem('authToken')
            })
            .build();

        try {
            await this.connection.start();
            console.log('SignalR Connected');
        } catch (error) {
            console.error('SignalR Connection Error:', error);
        }
    }

    subscribeToVehicle(vehicleId, callback) {
        if (this.connection) {
            this.connection.invoke('SubscribeToVehicle', vehicleId);
            this.connection.on(`VehicleUpdate_${vehicleId}`, callback);
        }
    }

    unsubscribeFromVehicle(vehicleId) {
        if (this.connection) {
            this.connection.invoke('UnsubscribeFromVehicle', vehicleId);
            this.connection.off(`VehicleUpdate_${vehicleId}`);
        }
    }
}

export default new SignalRService();
```

## 💡 Interview Success Strategies

### Time Management Tips
1. **Start Simple**: Get basic CRUD working before adding complexity
2. **Use AI Strategically**: Generate boilerplate, then customize for requirements
3. **Test Early**: Write tests as you go, don't leave them for the end
4. **Communicate**: Explain your approach and ask clarifying questions

### Common Pitfalls to Avoid
- Don't over-engineer the solution
- Don't forget error handling and validation
- Don't skip the testing phase
- Don't ignore performance considerations

### Key Questions to Ask
- "What's the expected data volume for this feature?"
- "Are there any specific performance requirements?"
- "Should I prioritize real-time updates or data consistency?"
- "What level of error handling detail do you want to see?"

## 🎯 Final Checklist

### Before Starting Each Phase
- [ ] Understand the requirements clearly
- [ ] Plan your approach (2-3 minutes)
- [ ] Identify which AI prompts to use
- [ ] Set up your development environment

### During Development
- [ ] Use provided code templates as starting points
- [ ] Leverage AI for boilerplate generation
- [ ] Test functionality as you build
- [ ] Explain your decisions to the interviewer

### Before Moving to Next Phase
- [ ] Verify current functionality works
- [ ] Run any tests you've written
- [ ] Commit your changes (if using version control)
- [ ] Briefly summarize what you've accomplished

---

**Remember**: This is a collaborative coding session, not a test to trip you up. Use this guide as your reference, leverage AI tools effectively, and focus on demonstrating your problem-solving approach and technical communication skills.

## 🔧 Essential AI Prompts Library

### Database & SQL Prompts

#### Complex Query Generation
```
I need a SQL query for fleet management analysis:

Context: Fleet management system with tables: Vehicles, Drivers, TelemetryData, Alerts, Routes
Goal: [Specific analysis like "Find vehicles with unusual fuel consumption patterns"]
Time Period: [Last 24 hours, past week, current month]
Grouping: [By vehicle, by driver, by route, by time period]
Metrics: [Speed, fuel consumption, engine hours, alerts count]

Requirements:
- Use SQL Server syntax
- Include proper JOINs and WHERE clauses
- Add window functions for ranking/partitioning if needed
- Optimize for performance with appropriate indexes
- Return results suitable for dashboard display

Generate the complete query with comments explaining the logic.
```

#### Performance Optimization
```
Optimize this SQL query for a fleet management system:

[Paste your existing query here]

Context:
- Table sizes: Vehicles (~10K), TelemetryData (~1M records/day), Drivers (~1K)
- Query frequency: [Real-time, hourly reports, daily analytics]
- Performance target: [Sub-second response, under 5 seconds, etc.]

Please provide:
1. Optimized query with explanations
2. Recommended indexes
3. Alternative approaches if applicable
4. Potential bottlenecks to watch for
```

### Backend Development Prompts

#### CQRS Implementation
```
Generate a complete CQRS implementation for FleetXQ:

Feature: [Vehicle Management, Driver Assignment, Telemetry Processing, Alert Management]
Operation: [Create, Update, Delete, Get, List, Process, Assign]
Entity: [Vehicle, Driver, TelemetryData, Alert, Route]

Context:
- .NET 8 Web API with Clean Architecture
- MediatR for CQRS pattern
- FluentValidation for input validation
- AutoMapper for DTO mapping
- Entity Framework Core with repository pattern
- Result pattern for error handling

Generate:
1. Command/Query class with proper validation attributes
2. Handler class with business logic
3. Validator class using FluentValidation
4. DTO classes for request/response
5. Controller action method
6. Repository interface method (if new)

Follow existing codebase patterns and include proper error handling, logging, and async/await.
```

#### Repository Pattern
```
Create a repository interface and implementation for [EntityName] in FleetXQ:

Entity Details:
- Primary purpose: [Brief description]
- Key relationships: [Related entities]
- Special operations needed: [Domain-specific queries]

Requirements:
- Follow existing IRepository<T> pattern
- Include standard CRUD operations
- Add domain-specific query methods
- Use Entity Framework Core
- Include proper async/await and cancellation tokens
- Add appropriate error handling
- Follow database-first approach

Generate both interface and implementation with XML documentation.
```

#### API Controller Generation
```
Create a REST API controller for FleetXQ:

Controller: [ControllerName]Controller
Entity: [EntityName]
Operations needed: [GET, POST, PUT, DELETE, custom operations]

Requirements:
- Inherit from BaseApiController
- Use MediatR for command/query dispatch
- Include proper HTTP status codes
- Add authorization attributes based on roles:
  * Admin: Full access
  * Manager: Read/write operational data
  * Driver: Limited to assigned resources
- Include proper error handling with ApiResponse wrapper
- Add XML documentation for API docs
- Follow RESTful conventions

Generate complete controller with all CRUD operations and custom endpoints.
```

### Testing Prompts

#### Unit Test Generation
```
Generate comprehensive unit tests for [HandlerName] in FleetXQ:

Handler Type: [CommandHandler/QueryHandler]
Feature: [Brief description of functionality]
Dependencies: [List of injected dependencies]

Test Requirements:
- Use [CommandHandlerTestBase<T>/QueryHandlerTestBase<T>] as base class
- Include these scenarios:
  * Happy path with valid input
  * Invalid input validation failures
  * Business rule violations
  * Repository/service exceptions
  * Edge cases specific to the feature
- Use FluentAssertions for assertions
- Mock all dependencies with Moq
- Follow AAA pattern (Arrange, Act, Assert)
- Verify repository calls and logging
- Include cancellation token testing

Generate complete test class with all test methods and proper setup.
```

#### Integration Test Creation
```
Create integration tests for [FeatureName] API endpoints:

Endpoints to test:
- [List specific endpoints like GET /api/vehicles, POST /api/vehicles, etc.]

Requirements:
- Use WebApplicationFactory for test server
- Include authentication testing with different roles
- Test both success and error scenarios
- Validate HTTP status codes and response format
- Include database state verification
- Test concurrent access scenarios if applicable
- Use in-memory database for isolation

Generate complete integration test class with setup and teardown.
```

### Frontend Development Prompts

#### React Component Generation
```
Create a React component for FleetXQ dashboard:

Component: [ComponentName]
Purpose: [Brief description of functionality]
Data: [What data it displays/manages]

Features needed:
- [CRUD operations, real-time updates, filtering, pagination, etc.]
- Form handling with validation
- API integration with error handling
- Loading states and user feedback
- Responsive design

Technical Requirements:
- Use functional components with hooks
- Implement Redux Toolkit for state management
- Include TypeScript types
- Use modern JavaScript (ES6+)
- Follow React best practices
- Include proper error boundaries
- Add accessibility features

API endpoints: [List available endpoints]
State structure: [Describe expected Redux state shape]
```

#### State Management Setup
```
Create Redux Toolkit slice for [FeatureName] in FleetXQ:

Feature: [Vehicle management, driver assignment, telemetry monitoring, etc.]
Operations: [List CRUD and custom operations]
API endpoints: [List relevant endpoints]

Requirements:
- Use createSlice and createAsyncThunk
- Include proper error handling
- Add loading states for async operations
- Implement optimistic updates where appropriate
- Include proper TypeScript types
- Add selectors for computed state
- Handle real-time updates from SignalR

Generate complete slice with actions, reducers, and selectors.
```

#### Chart/Visualization Component
```
Create a data visualization component for FleetXQ:

Chart Type: [Line chart, bar chart, pie chart, heatmap, etc.]
Data Source: [Telemetry data, vehicle status, alert trends, performance metrics]
Update Frequency: [Real-time, every 30 seconds, hourly, etc.]

Features:
- Interactive tooltips and legends
- Time range selection (1h, 24h, 7d, 30d)
- Data filtering and grouping options
- Export functionality (PNG, PDF, CSV)
- Responsive design for mobile/desktop
- Real-time updates via SignalR

Technical Requirements:
- Use Chart.js or D3.js
- Implement proper data fetching with error handling
- Add loading states and empty state handling
- Optimize for performance with large datasets
- Include accessibility features
- Support multiple data series

Data format: [Describe expected API response structure]
```

## 🎯 Phase-Specific Quick Start Commands

### Phase 1: Database Setup
```bash
# Create new migration
dotnet ef migrations add InitialCreate --project FleetXQ.Infrastructure

# Update database
dotnet ef database update --project FleetXQ.Infrastructure

# Scaffold existing database
dotnet ef dbcontext scaffold "Server=localhost;Database=FleetXQ;Trusted_Connection=true;" Microsoft.EntityFrameworkCore.SqlServer --output-dir Models --data-annotations
```

### Phase 2: Backend API
```bash
# Create new Web API project
dotnet new webapi -n FleetXQ.Api

# Add required packages
dotnet add package MediatR
dotnet add package FluentValidation.AspNetCore
dotnet add package AutoMapper.Extensions.Microsoft.DependencyInjection
dotnet add package Microsoft.EntityFrameworkCore.SqlServer

# Run the API
dotnet run --project FleetXQ.Api
```

### Phase 3: Testing Setup
```bash
# Create test project
dotnet new xunit -n FleetXQ.Application.Tests

# Add test packages
dotnet add package Moq
dotnet add package FluentAssertions
dotnet add package Microsoft.AspNetCore.Mvc.Testing

# Run tests
dotnet test
```

### Phase 4: Frontend Setup
```bash
# Create React app
npx create-react-app fleetxq-frontend --template typescript

# Add required packages
npm install @reduxjs/toolkit react-redux axios chart.js react-chartjs-2 @microsoft/signalr

# Start development server
npm start
```

## 🔍 Debugging & Troubleshooting Guide

### Common Backend Issues

#### MediatR Handler Not Found
```csharp
// Ensure handler is registered in DI container
services.AddMediatR(typeof(CreateVehicleCommandHandler).Assembly);

// Check handler implements correct interface
public class CreateVehicleCommandHandler : IRequestHandler<CreateVehicleCommand, Result<VehicleDto>>
```

#### EF Core Migration Issues
```bash
# Reset migrations
dotnet ef database drop
dotnet ef migrations remove
dotnet ef migrations add InitialCreate
dotnet ef database update
```

#### Validation Not Working
```csharp
// Register FluentValidation
services.AddFluentValidationAutoValidation();
services.AddValidatorsFromAssembly(typeof(CreateVehicleCommandValidator).Assembly);
```

### Common Frontend Issues

#### CORS Errors
```csharp
// In Program.cs
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp",
        policy => policy
            .WithOrigins("http://localhost:3000")
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials());
});

app.UseCors("AllowReactApp");
```

#### SignalR Connection Issues
```javascript
// Check authentication token
const connection = new signalR.HubConnectionBuilder()
    .withUrl('/telemetryHub', {
        accessTokenFactory: () => localStorage.getItem('authToken')
    })
    .configureLogging(signalR.LogLevel.Debug) // Add for debugging
    .build();
```

## 📚 Quick Reference APIs

### FleetXQ API Endpoints
```
Authentication:
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout

Vehicles:
GET /api/vehicles
GET /api/vehicles/{id}
POST /api/vehicles
PUT /api/vehicles/{id}
DELETE /api/vehicles/{id}
PUT /api/vehicles/{id}/status

Drivers:
GET /api/drivers
POST /api/drivers
PUT /api/drivers/{id}/assign-vehicle

Telemetry:
GET /api/telemetry/{vehicleId}/latest
GET /api/telemetry/{vehicleId}/history
POST /api/telemetry/batch

Alerts:
GET /api/alerts
POST /api/alerts/{id}/acknowledge
GET /api/alerts/active
```

### HTTP Status Codes Reference
```
200 OK - Successful GET, PUT
201 Created - Successful POST
204 No Content - Successful DELETE
400 Bad Request - Validation errors
401 Unauthorized - Authentication required
403 Forbidden - Insufficient permissions
404 Not Found - Resource doesn't exist
409 Conflict - Resource already exists
500 Internal Server Error - Server error
```

Good luck with your interview! 🚀
