namespace FleetXQ.Application.Interfaces;

/// <summary>
/// Service for password validation and security requirements
/// </summary>
public interface IPasswordValidationService
{
    /// <summary>
    /// Validates a password against security requirements
    /// </summary>
    /// <param name="password">The password to validate</param>
    /// <returns>The validation result</returns>
    PasswordValidationResult ValidatePassword(string password);

    /// <summary>
    /// Gets the password requirements description
    /// </summary>
    /// <returns>The password requirements</returns>
    string GetPasswordRequirements();
}

/// <summary>
/// Result of password validation
/// </summary>
public sealed class PasswordValidationResult
{
    /// <summary>
    /// Gets or sets a value indicating whether the password is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Gets or sets the validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Creates a successful validation result
    /// </summary>
    /// <returns>A successful result</returns>
    public static PasswordValidationResult Success()
    {
        return new PasswordValidationResult { IsValid = true };
    }

    /// <summary>
    /// Creates a failed validation result
    /// </summary>
    /// <param name="errors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static PasswordValidationResult Failed(params string[] errors)
    {
        return new PasswordValidationResult
        {
            IsValid = false,
            Errors = errors.ToList()
        };
    }
}
