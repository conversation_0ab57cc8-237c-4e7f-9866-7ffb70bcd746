using FleetXQ.Infrastructure.Authentication;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace FleetXQ.Application.Tests.Infrastructure;

/// <summary>
/// Unit tests for PasswordHashingService
/// </summary>
public sealed class PasswordHashingServiceTests
{
    private readonly Mock<ILogger<PasswordHashingService>> _mockLogger;
    private readonly PasswordHashingService _passwordHashingService;

    public PasswordHashingServiceTests()
    {
        _mockLogger = new Mock<ILogger<PasswordHashingService>>();
        _passwordHashingService = new PasswordHashingService(_mockLogger.Object);
    }

    [Fact]
    public void HashPassword_ValidPassword_ReturnsHashedPassword()
    {
        // Arrange
        var password = "TestPassword123!";

        // Act
        var hashedPassword = _passwordHashingService.HashPassword(password);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        hashedPassword.Should().NotBe(password);
        hashedPassword.Should().StartWith("$2a$"); // BCrypt hash format
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void HashPassword_InvalidPassword_ThrowsArgumentException(string? password)
    {
        // Act & Assert
        var action = () => _passwordHashingService.HashPassword(password!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void VerifyPassword_CorrectPassword_ReturnsTrue()
    {
        // Arrange
        var password = "TestPassword123!";
        var hashedPassword = _passwordHashingService.HashPassword(password);

        // Act
        var isValid = _passwordHashingService.VerifyPassword(password, hashedPassword);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void VerifyPassword_IncorrectPassword_ReturnsFalse()
    {
        // Arrange
        var password = "TestPassword123!";
        var wrongPassword = "WrongPassword123!";
        var hashedPassword = _passwordHashingService.HashPassword(password);

        // Act
        var isValid = _passwordHashingService.VerifyPassword(wrongPassword, hashedPassword);

        // Assert
        isValid.Should().BeFalse();
    }

    [Theory]
    [InlineData("", "valid_hash")]
    [InlineData("   ", "valid_hash")]
    [InlineData(null, "valid_hash")]
    public void VerifyPassword_InvalidPassword_ReturnsFalse(string? password, string hash)
    {
        // Act
        var isValid = _passwordHashingService.VerifyPassword(password!, hash);

        // Assert
        isValid.Should().BeFalse();
    }

    [Theory]
    [InlineData("valid_password", "")]
    [InlineData("valid_password", "   ")]
    [InlineData("valid_password", null)]
    public void VerifyPassword_InvalidHash_ReturnsFalse(string password, string? hash)
    {
        // Act
        var isValid = _passwordHashingService.VerifyPassword(password, hash!);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void VerifyPassword_InvalidHashFormat_ReturnsFalse()
    {
        // Arrange
        var password = "TestPassword123!";
        var invalidHash = "invalid_hash_format";

        // Act
        var isValid = _passwordHashingService.VerifyPassword(password, invalidHash);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void HashPassword_SamePasswordTwice_GeneratesDifferentHashes()
    {
        // Arrange
        var password = "TestPassword123!";

        // Act
        var hash1 = _passwordHashingService.HashPassword(password);
        var hash2 = _passwordHashingService.HashPassword(password);

        // Assert
        hash1.Should().NotBe(hash2);
        
        // But both should verify correctly
        _passwordHashingService.VerifyPassword(password, hash1).Should().BeTrue();
        _passwordHashingService.VerifyPassword(password, hash2).Should().BeTrue();
    }

    [Theory]
    [InlineData("password")]
    [InlineData("Password123")]
    [InlineData("MyVeryLongPasswordThatShouldStillWork123!")]
    [InlineData("短密码")]
    [InlineData("пароль123")]
    public void HashPassword_VariousPasswords_WorksCorrectly(string password)
    {
        // Act
        var hashedPassword = _passwordHashingService.HashPassword(password);
        var isValid = _passwordHashingService.VerifyPassword(password, hashedPassword);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        hashedPassword.Should().NotBe(password);
        isValid.Should().BeTrue();
    }

    [Fact]
    public void HashPassword_LongPassword_WorksCorrectly()
    {
        // Arrange
        var longPassword = new string('a', 1000); // 1000 character password

        // Act
        var hashedPassword = _passwordHashingService.HashPassword(longPassword);
        var isValid = _passwordHashingService.VerifyPassword(longPassword, hashedPassword);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        isValid.Should().BeTrue();
    }

    [Fact]
    public void VerifyPassword_CaseSensitive_WorksCorrectly()
    {
        // Arrange
        var password = "TestPassword123!";
        var hashedPassword = _passwordHashingService.HashPassword(password);

        // Act & Assert
        _passwordHashingService.VerifyPassword(password, hashedPassword).Should().BeTrue();
        _passwordHashingService.VerifyPassword("testpassword123!", hashedPassword).Should().BeFalse();
        _passwordHashingService.VerifyPassword("TESTPASSWORD123!", hashedPassword).Should().BeFalse();
    }

    [Fact]
    public void HashPassword_SpecialCharacters_WorksCorrectly()
    {
        // Arrange
        var passwordWithSpecialChars = "P@ssw0rd!#$%^&*()_+-=[]{}|;:,.<>?";

        // Act
        var hashedPassword = _passwordHashingService.HashPassword(passwordWithSpecialChars);
        var isValid = _passwordHashingService.VerifyPassword(passwordWithSpecialChars, hashedPassword);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        isValid.Should().BeTrue();
    }

    [Fact]
    public void VerifyPassword_SlightlyDifferentPasswords_ReturnsFalse()
    {
        // Arrange
        var password = "TestPassword123!";
        var hashedPassword = _passwordHashingService.HashPassword(password);

        // Act & Assert
        _passwordHashingService.VerifyPassword("TestPassword123", hashedPassword).Should().BeFalse(); // Missing !
        _passwordHashingService.VerifyPassword("TestPassword124!", hashedPassword).Should().BeFalse(); // Different number
        _passwordHashingService.VerifyPassword("TestPassword123! ", hashedPassword).Should().BeFalse(); // Extra space
        _passwordHashingService.VerifyPassword(" TestPassword123!", hashedPassword).Should().BeFalse(); // Leading space
    }
}
