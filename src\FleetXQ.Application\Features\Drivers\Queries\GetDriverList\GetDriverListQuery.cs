using FleetXQ.Domain.Enums;
using MediatR;

namespace FleetXQ.Application.Features.Drivers.Queries.GetDriverList;

/// <summary>
/// Query to get a list of drivers
/// </summary>
public sealed class GetDriverListQuery : IRequest<GetDriverListResult>
{
    /// <summary>
    /// Gets or sets the page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Gets or sets the page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Gets or sets the status filter
    /// </summary>
    public DriverStatus? Status { get; set; }

    /// <summary>
    /// Gets or sets the search term
    /// </summary>
    public string? SearchTerm { get; set; }
}

/// <summary>
/// DTO for driver list items
/// </summary>
public sealed class DriverListDto
{
    /// <summary>
    /// Gets or sets the driver ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the full name
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Gets or sets the license number
    /// </summary>
    public string? LicenseNumber { get; set; }

    /// <summary>
    /// Gets or sets the status
    /// </summary>
    public DriverStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the phone number
    /// </summary>
    public string? Phone { get; set; }
}

/// <summary>
/// Result of getting driver list
/// </summary>
public sealed class GetDriverListResult
{
    /// <summary>
    /// Gets or sets the drivers
    /// </summary>
    public IEnumerable<DriverListDto> Drivers { get; set; } = new List<DriverListDto>();

    /// <summary>
    /// Gets or sets the total count
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Gets or sets the page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Gets or sets the page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Gets the total pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="drivers">The drivers</param>
    /// <param name="totalCount">The total count</param>
    /// <param name="pageNumber">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <returns>A successful result</returns>
    public static GetDriverListResult Successful(IEnumerable<DriverListDto> drivers, int totalCount, int pageNumber, int pageSize)
    {
        return new GetDriverListResult
        {
            Drivers = drivers,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            Success = true
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static GetDriverListResult Failed(string errorMessage)
    {
        return new GetDriverListResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
