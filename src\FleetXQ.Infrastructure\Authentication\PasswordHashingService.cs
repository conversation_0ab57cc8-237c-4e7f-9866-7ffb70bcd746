using FleetXQ.Application.Interfaces;
using Microsoft.Extensions.Logging;
using BCrypt.Net;

namespace FleetXQ.Infrastructure.Authentication;

/// <summary>
/// Service for password hashing and verification using BCrypt
/// </summary>
public sealed class PasswordHashingService : IPasswordHashingService
{
    private readonly ILogger<PasswordHashingService> _logger;
    private const int WorkFactor = 12; // BCrypt work factor for security vs performance balance

    /// <summary>
    /// Initializes a new instance of the <see cref="PasswordHashingService"/> class
    /// </summary>
    /// <param name="logger">The logger</param>
    public PasswordHashingService(ILogger<PasswordHashingService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Hashes a password using BCrypt
    /// </summary>
    /// <param name="password">The password to hash</param>
    /// <returns>The hashed password</returns>
    public string HashPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be null or empty", nameof(password));

        try
        {
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(password, WorkFactor);
            
            _logger.LogDebug("Password hashed successfully with work factor {WorkFactor}", WorkFactor);
            
            return hashedPassword;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error hashing password");
            throw;
        }
    }

    /// <summary>
    /// Verifies a password against a hash using BCrypt
    /// </summary>
    /// <param name="password">The password to verify</param>
    /// <param name="hash">The hash to verify against</param>
    /// <returns>True if the password matches the hash</returns>
    public bool VerifyPassword(string password, string hash)
    {
        if (string.IsNullOrWhiteSpace(password))
        {
            _logger.LogDebug("Password verification failed - password is null or empty");
            return false;
        }

        if (string.IsNullOrWhiteSpace(hash))
        {
            _logger.LogDebug("Password verification failed - hash is null or empty");
            return false;
        }

        try
        {
            var isValid = BCrypt.Net.BCrypt.Verify(password, hash);
            
            _logger.LogDebug("Password verification result: {IsValid}", isValid);
            
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying password");
            return false;
        }
    }
}
