using FleetXQ.Api.Tests.Integration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace FleetXQ.Api.Tests.TestRunner;

/// <summary>
/// Integration test runner for validating the complete test suite
/// </summary>
public class IntegrationTestRunner : IClassFixture<IntegrationTestWebApplicationFactory>
{
    private readonly IntegrationTestWebApplicationFactory _factory;
    private readonly ITestOutputHelper _output;

    public IntegrationTestRunner(IntegrationTestWebApplicationFactory factory, ITestOutputHelper output)
    {
        _factory = factory;
        _output = output;
    }

    [Fact]
    public async Task ValidateIntegrationTestInfrastructure_ShouldPassAllChecks()
    {
        var stopwatch = Stopwatch.StartNew();
        var results = new List<ValidationResult>();

        try
        {
            // 1. Validate WebApplicationFactory Configuration
            results.Add(await ValidateWebApplicationFactoryAsync());

            // 2. Validate Database Setup
            results.Add(await ValidateDatabaseSetupAsync());

            // 3. Validate Authentication Infrastructure
            results.Add(await ValidateAuthenticationInfrastructureAsync());

            // 4. Validate Test Data Builders
            results.Add(await ValidateTestDataBuildersAsync());

            // 5. Validate API Integration Tests
            results.Add(await ValidateApiIntegrationTestsAsync());

            // 6. Validate SignalR Integration Tests
            results.Add(await ValidateSignalRIntegrationTestsAsync());

            // 7. Validate Test Isolation
            results.Add(await ValidateTestIsolationAsync());

            // 8. Validate Performance Testing
            results.Add(await ValidatePerformanceTestingAsync());

            stopwatch.Stop();

            // Report results
            _output.WriteLine($"Integration Test Suite Validation completed in {stopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"Total checks: {results.Count}");
            _output.WriteLine($"Passed: {results.Count(r => r.Success)}");
            _output.WriteLine($"Failed: {results.Count(r => !r.Success)}");

            foreach (var result in results)
            {
                var status = result.Success ? "✓" : "✗";
                _output.WriteLine($"{status} {result.TestName}: {result.Message}");
                
                if (!result.Success && result.Exception != null)
                {
                    _output.WriteLine($"  Exception: {result.Exception.Message}");
                }
            }

            // Assert all validations passed
            var failedResults = results.Where(r => !r.Success).ToList();
            if (failedResults.Any())
            {
                var failureMessages = string.Join("\n", failedResults.Select(r => $"- {r.TestName}: {r.Message}"));
                throw new InvalidOperationException($"Integration test validation failed:\n{failureMessages}");
            }
        }
        finally
        {
            await _factory.ClearDatabaseAsync();
        }
    }

    private async Task<ValidationResult> ValidateWebApplicationFactoryAsync()
    {
        try
        {
            // Test that the factory can create clients
            using var client = _factory.CreateClient();
            client.Should().NotBeNull();

            // Test that services are properly registered
            var dbContext = _factory.GetRequiredService<FleetXQ.Infrastructure.Data.ApplicationDbContext>();
            dbContext.Should().NotBeNull();

            // Test that the database can be created
            await dbContext.Database.EnsureCreatedAsync();

            return ValidationResult.Success("WebApplicationFactory Configuration", "Factory properly configured with all services");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("WebApplicationFactory Configuration", $"Factory configuration failed: {ex.Message}", ex);
        }
    }

    private async Task<ValidationResult> ValidateDatabaseSetupAsync()
    {
        try
        {
            // Test database creation and cleanup
            await _factory.ResetDatabaseAsync();
            
            using var dbContext = _factory.CreateDbContext();
            
            // Test that all DbSets are accessible
            var userCount = dbContext.Users.Count();
            var vehicleCount = dbContext.Vehicles.Count();
            var driverCount = dbContext.Drivers.Count();
            var alertCount = dbContext.Alerts.Count();
            var tripCount = dbContext.Trips.Count();

            // All should be 0 in a fresh database
            (userCount + vehicleCount + driverCount + alertCount + tripCount).Should().Be(0);

            return ValidationResult.Success("Database Setup", "In-memory database properly configured and accessible");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("Database Setup", $"Database setup failed: {ex.Message}", ex);
        }
    }

    private async Task<ValidationResult> ValidateAuthenticationInfrastructureAsync()
    {
        try
        {
            // Test JWT token creation
            using var client = _factory.CreateClient();
            var userId = Guid.NewGuid();
            
            // This would use the same logic as ApiIntegrationTestBase
            var tokenService = _factory.GetRequiredService<FleetXQ.Application.Interfaces.ITokenService>();
            tokenService.Should().NotBeNull();

            return ValidationResult.Success("Authentication Infrastructure", "JWT authentication properly configured");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("Authentication Infrastructure", $"Authentication setup failed: {ex.Message}", ex);
        }
    }

    private async Task<ValidationResult> ValidateTestDataBuildersAsync()
    {
        try
        {
            // Test all test data builders
            var user = FleetXQ.Api.Tests.Builders.TestDataBuilder.CreateUser();
            var vehicle = FleetXQ.Api.Tests.Builders.TestDataBuilder.CreateVehicle();
            var driver = FleetXQ.Api.Tests.Builders.TestDataBuilder.CreateDriver();
            var alert = FleetXQ.Api.Tests.Builders.TestDataBuilder.CreateAlert(vehicle.Id);
            var trip = FleetXQ.Api.Tests.Builders.TestDataBuilder.CreateTrip(vehicle.Id, driver.Id);

            // Validate entities are properly created
            user.Should().NotBeNull();
            user.Username.Should().NotBeNullOrEmpty();
            
            vehicle.Should().NotBeNull();
            vehicle.Name.Should().NotBeNullOrEmpty();
            
            driver.Should().NotBeNull();
            driver.FirstName.Should().NotBeNullOrEmpty();
            
            alert.Should().NotBeNull();
            alert.VehicleId.Should().Be(vehicle.Id);
            
            trip.Should().NotBeNull();
            trip.VehicleId.Should().Be(vehicle.Id);

            return ValidationResult.Success("Test Data Builders", "All test data builders working correctly");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("Test Data Builders", $"Test data builders failed: {ex.Message}", ex);
        }
    }

    private async Task<ValidationResult> ValidateApiIntegrationTestsAsync()
    {
        try
        {
            // Test that API integration test base works
            var testInstance = new TestApiIntegrationTest(_factory);
            await testInstance.TestBasicFunctionality();

            return ValidationResult.Success("API Integration Tests", "API integration test infrastructure working");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("API Integration Tests", $"API integration tests failed: {ex.Message}", ex);
        }
    }

    private async Task<ValidationResult> ValidateSignalRIntegrationTestsAsync()
    {
        try
        {
            // Test SignalR connection capability
            var userId = Guid.NewGuid();
            var signalRBase = new TestSignalRIntegration(_factory);
            await signalRBase.TestConnectionCapability(userId);

            return ValidationResult.Success("SignalR Integration Tests", "SignalR integration test infrastructure working");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("SignalR Integration Tests", $"SignalR integration tests failed: {ex.Message}", ex);
        }
    }

    private async Task<ValidationResult> ValidateTestIsolationAsync()
    {
        try
        {
            // Test 1: Add data
            await _factory.SeedDatabaseAsync(async context =>
            {
                var user = FleetXQ.Api.Tests.Builders.TestDataBuilder.CreateUser();
                context.Users.Add(user);
            });

            using (var dbContext = _factory.CreateDbContext())
            {
                dbContext.Users.Count().Should().Be(1);
            }

            // Test 2: Clear data
            await _factory.ClearDatabaseAsync();

            using (var dbContext = _factory.CreateDbContext())
            {
                dbContext.Users.Count().Should().Be(0);
            }

            // Test 3: Add different data
            await _factory.SeedDatabaseAsync(async context =>
            {
                var vehicle = FleetXQ.Api.Tests.Builders.TestDataBuilder.CreateVehicle();
                context.Vehicles.Add(vehicle);
            });

            using (var dbContext = _factory.CreateDbContext())
            {
                dbContext.Users.Count().Should().Be(0);
                dbContext.Vehicles.Count().Should().Be(1);
            }

            return ValidationResult.Success("Test Isolation", "Tests properly isolated with clean database state");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("Test Isolation", $"Test isolation failed: {ex.Message}", ex);
        }
    }

    private async Task<ValidationResult> ValidatePerformanceTestingAsync()
    {
        try
        {
            // Validate that NBomber is available and can run basic scenarios
            var hasNBomber = AppDomain.CurrentDomain.GetAssemblies()
                .Any(a => a.GetName().Name?.Contains("NBomber") == true);

            if (!hasNBomber)
            {
                return ValidationResult.Failure("Performance Testing", "NBomber not available for performance testing");
            }

            return ValidationResult.Success("Performance Testing", "Performance testing infrastructure available");
        }
        catch (Exception ex)
        {
            return ValidationResult.Failure("Performance Testing", $"Performance testing validation failed: {ex.Message}", ex);
        }
    }

    private class ValidationResult
    {
        public string TestName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Exception? Exception { get; set; }

        public static ValidationResult Success(string testName, string message)
        {
            return new ValidationResult { TestName = testName, Success = true, Message = message };
        }

        public static ValidationResult Failure(string testName, string message, Exception? exception = null)
        {
            return new ValidationResult { TestName = testName, Success = false, Message = message, Exception = exception };
        }
    }

    private class TestApiIntegrationTest : ApiIntegrationTestBase
    {
        public TestApiIntegrationTest(IntegrationTestWebApplicationFactory factory) : base(factory)
        {
        }

        public async Task TestBasicFunctionality()
        {
            // Test token creation
            var token = CreateTestJwtToken(Guid.NewGuid(), "testuser");
            token.Should().NotBeNullOrEmpty();

            // Test HTTP client creation
            SetAuthorizationHeader(token);
            HttpClient.DefaultRequestHeaders.Authorization.Should().NotBeNull();
        }
    }

    private class TestSignalRIntegration : SignalRIntegrationTestBase
    {
        public TestSignalRIntegration(IntegrationTestWebApplicationFactory factory) : base(factory)
        {
        }

        public async Task TestConnectionCapability(Guid userId)
        {
            var token = CreateTestJwtToken(userId, "testuser", "User");
            token.Should().NotBeNullOrEmpty();

            // Test connection creation (without actually connecting to avoid complexity)
            var connection = await CreateHubConnectionAsync("/hubs/telemetry", token);
            connection.Should().NotBeNull();
            await connection.DisposeAsync();
        }
    }
}
