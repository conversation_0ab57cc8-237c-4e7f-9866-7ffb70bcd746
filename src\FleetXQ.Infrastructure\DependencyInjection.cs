using FleetXQ.Application.Common.Interfaces;
using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Authentication;
using FleetXQ.Infrastructure.Data;
using FleetXQ.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.Infrastructure;

/// <summary>
/// Extension methods for configuring infrastructure services
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Adds infrastructure services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database configuration
        services.AddDatabaseServices(configuration);

        // Authentication Services
        services.AddAuthenticationServices();

        // Repository registrations will be added here
        // External service integrations will be added here

        // SignalR Services
        services.AddSignalRServices();

        return services;
    }

    /// <summary>
    /// Adds database services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection</returns>
    private static IServiceCollection AddDatabaseServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Add Entity Framework DbContext
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            if (!string.IsNullOrEmpty(connectionString))
            {
                options.UseSqlServer(connectionString);
            }
            else
            {
                // Fallback to in-memory database for testing
                options.UseInMemoryDatabase("FleetXQTestDb");
            }
        });

        // Register IApplicationDbContext
        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

        return services;
    }

    /// <summary>
    /// Adds authentication services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection</returns>
    private static IServiceCollection AddAuthenticationServices(this IServiceCollection services)
    {
        services.AddScoped<ITokenService, TokenService>();
        services.AddScoped<IPasswordHashingService, PasswordHashingService>();
        services.AddScoped<IAuthorizationService, AuthorizationService>();
        services.AddScoped<IPasswordValidationService, PasswordValidationService>();
        services.AddScoped<IRateLimitingService, RateLimitingService>();

        // Add memory cache for rate limiting
        services.AddMemoryCache();

        return services;
    }

    /// <summary>
    /// Adds SignalR-related services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection</returns>
    public static IServiceCollection AddSignalRServices(this IServiceCollection services)
    {
        // Register SignalR connection manager
        services.AddSingleton<ISignalRConnectionManager, SignalRConnectionManager>();

        // Register SignalR notification service
        services.AddScoped<ISignalRNotificationService, SignalRNotificationService>();

        // Register SignalR connection state service
        services.AddSingleton<SignalRConnectionStateService>();

        return services;
    }
}
