using FleetXQ.Domain.Enums;
using System.Security.Claims;

namespace FleetXQ.Application.Interfaces;

/// <summary>
/// Service for authorization and permission checking
/// </summary>
public interface IAuthorizationService
{
    /// <summary>
    /// Checks if a user has a specific role
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="requiredRole">The required role</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the user has the required role</returns>
    Task<bool> HasRoleAsync(Guid userId, UserRole requiredRole, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a user has a specific permission
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="permission">The required permission</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the user has the required permission</returns>
    Task<bool> HasPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a user can access a specific resource
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="resourceType">The resource type</param>
    /// <param name="resourceId">The resource ID</param>
    /// <param name="action">The action to perform</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the user can access the resource</returns>
    Task<bool> CanAccessResourceAsync(Guid userId, string resourceType, Guid? resourceId, string action, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all permissions for a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The list of permissions</returns>
    Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates user context from claims
    /// </summary>
    /// <param name="claims">The user claims</param>
    /// <returns>The user context information</returns>
    UserContext? ValidateUserContext(ClaimsPrincipal claims);
}

/// <summary>
/// Represents user context information
/// </summary>
public sealed class UserContext
{
    /// <summary>
    /// Gets or sets the user ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Gets or sets the username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the user role
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the user is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Gets or sets the user permissions
    /// </summary>
    public List<string> Permissions { get; set; } = new();
}
