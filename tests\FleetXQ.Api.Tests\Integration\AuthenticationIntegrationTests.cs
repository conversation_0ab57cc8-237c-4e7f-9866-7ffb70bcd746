using FleetXQ.Api.Models;
using FleetXQ.Api.Tests.Builders;
using FleetXQ.Application.Features.Authentication.Commands.LoginUser;
using FleetXQ.Domain.Enums;
using FleetXQ.Infrastructure.Authentication;
using Microsoft.Extensions.DependencyInjection;
using System.Net;

namespace FleetXQ.Api.Tests.Integration;

/// <summary>
/// Integration tests for authentication endpoints
/// </summary>
public class AuthenticationIntegrationTests : ApiIntegrationTestBase
{
    public AuthenticationIntegrationTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task LoginUser_WithValidCredentials_ShouldReturnSuccessWithToken()
    {
        // Arrange
        var passwordHashingService = Factory.GetRequiredService<IPasswordHashingService>();
        var password = "TestPassword123!";
        var passwordHash = passwordHashingService.HashPassword(password);
        
        var user = TestDataBuilder.CreateUser(
            username: "testuser",
            email: "<EMAIL>",
            role: UserRole.User,
            isActive: true,
            isEmailConfirmed: true
        );

        // Update the password hash using reflection since it's private set
        var passwordHashProperty = typeof(Domain.Entities.User).GetProperty("PasswordHash");
        passwordHashProperty?.SetValue(user, passwordHash);

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
        });

        var loginRequest = new LoginUserCommand
        {
            Username = "testuser",
            Password = password
        };

        // Act
        var (response, content) = await PostAsync<LoginUserCommand, ApiResponse<LoginUserResult>>(
            "/api/auth/login", 
            loginRequest
        );

        // Assert
        AssertSuccessResponse(response, content);
        content!.Data.Should().NotBeNull();
        content.Data!.AccessToken.Should().NotBeNullOrEmpty();
        content.Data.RefreshToken.Should().NotBeNullOrEmpty();
        content.Data.User.Should().NotBeNull();
        content.Data.User!.Username.Should().Be("testuser");
        content.Data.User.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task LoginUser_WithInvalidCredentials_ShouldReturnUnauthorized()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser(
            username: "testuser",
            email: "<EMAIL>"
        );

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
        });

        var loginRequest = new LoginUserCommand
        {
            Username = "testuser",
            Password = "WrongPassword123!"
        };

        // Act
        var (response, content) = await PostAsync<LoginUserCommand, ApiResponse<LoginUserResult>>(
            "/api/auth/login", 
            loginRequest
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.Unauthorized);
        content!.Message.Should().Contain("Invalid username or password");
    }

    [Fact]
    public async Task LoginUser_WithInactiveUser_ShouldReturnUnauthorized()
    {
        // Arrange
        var passwordHashingService = Factory.GetRequiredService<IPasswordHashingService>();
        var password = "TestPassword123!";
        var passwordHash = passwordHashingService.HashPassword(password);
        
        var user = TestDataBuilder.CreateUser(
            username: "testuser",
            email: "<EMAIL>",
            isActive: false
        );

        // Update the password hash
        var passwordHashProperty = typeof(Domain.Entities.User).GetProperty("PasswordHash");
        passwordHashProperty?.SetValue(user, passwordHash);

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
        });

        var loginRequest = new LoginUserCommand
        {
            Username = "testuser",
            Password = password
        };

        // Act
        var (response, content) = await PostAsync<LoginUserCommand, ApiResponse<LoginUserResult>>(
            "/api/auth/login", 
            loginRequest
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.Unauthorized);
        content!.Message.Should().Contain("Account is not active");
    }

    [Fact]
    public async Task LoginUser_WithNonExistentUser_ShouldReturnUnauthorized()
    {
        // Arrange
        var loginRequest = new LoginUserCommand
        {
            Username = "nonexistent",
            Password = "TestPassword123!"
        };

        // Act
        var (response, content) = await PostAsync<LoginUserCommand, ApiResponse<LoginUserResult>>(
            "/api/auth/login", 
            loginRequest
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.Unauthorized);
        content!.Message.Should().Contain("Invalid username or password");
    }

    [Fact]
    public async Task LoginUser_WithEmptyCredentials_ShouldReturnBadRequest()
    {
        // Arrange
        var loginRequest = new LoginUserCommand
        {
            Username = "",
            Password = ""
        };

        // Act
        var (response, content) = await PostAsync<LoginUserCommand, ApiResponse<LoginUserResult>>(
            "/api/auth/login", 
            loginRequest
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task AuthenticatedEndpoint_WithValidToken_ShouldReturnSuccess()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser(
            username: "testuser",
            email: "<EMAIL>",
            role: UserRole.User
        );

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var response = await HttpClient.GetAsync("/api/vehicles");

        // Assert
        response.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task AuthenticatedEndpoint_WithoutToken_ShouldReturnUnauthorized()
    {
        // Arrange
        ClearAuthorizationHeader();

        // Act
        var response = await HttpClient.GetAsync("/api/vehicles");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task AuthenticatedEndpoint_WithExpiredToken_ShouldReturnUnauthorized()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var expiredToken = CreateTestJwtToken(
            user.Id, 
            user.Username, 
            user.Email, 
            new[] { user.Role.ToString() },
            expiryMinutes: -1 // Expired token
        );
        
        SetAuthorizationHeader(expiredToken);

        // Act
        var response = await HttpClient.GetAsync("/api/vehicles");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task AuthenticatedEndpoint_WithInvalidToken_ShouldReturnUnauthorized()
    {
        // Arrange
        SetAuthorizationHeader("invalid.jwt.token");

        // Act
        var response = await HttpClient.GetAsync("/api/vehicles");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task AdminEndpoint_WithAdminRole_ShouldReturnSuccess()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();
        var token = CreateTestJwtToken(
            adminUser.Id, 
            adminUser.Username, 
            adminUser.Email, 
            new[] { adminUser.Role.ToString() }
        );
        
        SetAuthorizationHeader(token);

        // Act - Try to access an admin endpoint (assuming users endpoint requires admin)
        var response = await HttpClient.GetAsync("/api/users");

        // Assert
        response.StatusCode.Should().NotBe(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task AdminEndpoint_WithUserRole_ShouldReturnForbidden()
    {
        // Arrange
        var regularUser = TestDataBuilder.CreateUser(role: UserRole.User);
        var token = CreateTestJwtToken(
            regularUser.Id, 
            regularUser.Username, 
            regularUser.Email, 
            new[] { regularUser.Role.ToString() }
        );
        
        SetAuthorizationHeader(token);

        // Act - Try to access an admin endpoint
        var response = await HttpClient.GetAsync("/api/users");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task LoginUser_MultipleFailedAttempts_ShouldLockAccount()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser(username: "testuser");

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
        });

        var loginRequest = new LoginUserCommand
        {
            Username = "testuser",
            Password = "WrongPassword123!"
        };

        // Act - Make multiple failed login attempts
        for (int i = 0; i < 5; i++)
        {
            await PostAsync<LoginUserCommand, ApiResponse<LoginUserResult>>(
                "/api/auth/login", 
                loginRequest
            );
        }

        // Try one more time after lockout
        var (response, content) = await PostAsync<LoginUserCommand, ApiResponse<LoginUserResult>>(
            "/api/auth/login", 
            loginRequest
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.Unauthorized);
        content!.Message.Should().Contain("Account is locked");
    }
}
