using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Authentication;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace FleetXQ.Application.Tests.Infrastructure;

/// <summary>
/// Unit tests for PasswordValidationService
/// </summary>
public sealed class PasswordValidationServiceTests
{
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<PasswordValidationService>> _mockLogger;
    private readonly PasswordValidationService _passwordValidationService;

    public PasswordValidationServiceTests()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<PasswordValidationService>>();

        // Setup default configuration
        _mockConfiguration.Setup(x => x.GetValue<int>("PasswordPolicy:MinLength", 8)).Returns(8);
        _mockConfiguration.Setup(x => x.GetValue<int>("PasswordPolicy:MaxLength", 128)).Returns(128);
        _mockConfiguration.Setup(x => x.GetValue<bool>("PasswordPolicy:RequireUppercase", true)).Returns(true);
        _mockConfiguration.Setup(x => x.GetValue<bool>("PasswordPolicy:RequireLowercase", true)).Returns(true);
        _mockConfiguration.Setup(x => x.GetValue<bool>("PasswordPolicy:RequireDigit", true)).Returns(true);
        _mockConfiguration.Setup(x => x.GetValue<bool>("PasswordPolicy:RequireSpecialChar", true)).Returns(true);
        _mockConfiguration.Setup(x => x.GetValue<int>("PasswordPolicy:MinUniqueChars", 4)).Returns(4);

        _passwordValidationService = new PasswordValidationService(_mockConfiguration.Object, _mockLogger.Object);
    }

    [Fact]
    public void ValidatePassword_ValidPassword_ReturnsSuccess()
    {
        // Arrange
        var password = "TestPassword123!";

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void ValidatePassword_EmptyOrNullPassword_ReturnsFailure(string? password)
    {
        // Act
        var result = _passwordValidationService.ValidatePassword(password!);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password is required");
    }

    [Fact]
    public void ValidatePassword_TooShortPassword_ReturnsFailure()
    {
        // Arrange
        var password = "Test1!"; // Only 6 characters

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password must be at least 8 characters long");
    }

    [Fact]
    public void ValidatePassword_TooLongPassword_ReturnsFailure()
    {
        // Arrange
        var password = new string('a', 129) + "A1!"; // 132 characters

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password cannot exceed 128 characters");
    }

    [Fact]
    public void ValidatePassword_NoUppercase_ReturnsFailure()
    {
        // Arrange
        var password = "testpassword123!";

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password must contain at least one uppercase letter");
    }

    [Fact]
    public void ValidatePassword_NoLowercase_ReturnsFailure()
    {
        // Arrange
        var password = "TESTPASSWORD123!";

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password must contain at least one lowercase letter");
    }

    [Fact]
    public void ValidatePassword_NoDigit_ReturnsFailure()
    {
        // Arrange
        var password = "TestPassword!";

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password must contain at least one digit");
    }

    [Fact]
    public void ValidatePassword_NoSpecialCharacter_ReturnsFailure()
    {
        // Arrange
        var password = "TestPassword123";

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)");
    }

    [Fact]
    public void ValidatePassword_NotEnoughUniqueCharacters_ReturnsFailure()
    {
        // Arrange
        var password = "Aaaa111!"; // Only 4 unique characters: A, a, 1, !

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password must contain at least 4 unique characters");
    }

    [Theory]
    [InlineData("Password123!")]
    [InlineData("TestPassword123!")]
    [InlineData("MyPassword456@")]
    public void ValidatePassword_ContainsWeakPattern_ReturnsFailure(string password)
    {
        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password contains common weak patterns and is not allowed");
    }

    [Theory]
    [InlineData("Test123456!")]
    [InlineData("TestAbc123!")]
    [InlineData("Test321Xyz!")]
    public void ValidatePassword_ContainsSequentialCharacters_ReturnsFailure(string password)
    {
        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Password cannot contain sequential characters (e.g., 123, abc)");
    }

    [Theory]
    [InlineData("MySecure1!")]
    [InlineData("StrongPass2@")]
    [InlineData("Complex3#")]
    [InlineData("Unique4$")]
    public void ValidatePassword_ValidPasswords_ReturnsSuccess(string password)
    {
        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void ValidatePassword_MultipleErrors_ReturnsAllErrors()
    {
        // Arrange
        var password = "test"; // Too short, no uppercase, no digit, no special char

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCountGreaterThan(1);
        result.Errors.Should().Contain("Password must be at least 8 characters long");
        result.Errors.Should().Contain("Password must contain at least one uppercase letter");
        result.Errors.Should().Contain("Password must contain at least one digit");
        result.Errors.Should().Contain("Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)");
    }

    [Fact]
    public void GetPasswordRequirements_ReturnsFormattedRequirements()
    {
        // Act
        var requirements = _passwordValidationService.GetPasswordRequirements();

        // Assert
        requirements.Should().NotBeNullOrEmpty();
        requirements.Should().Contain("Password must:");
        requirements.Should().Contain("Be between 8 and 128 characters long");
        requirements.Should().Contain("Contain at least one uppercase letter");
        requirements.Should().Contain("Contain at least one lowercase letter");
        requirements.Should().Contain("Contain at least one digit");
        requirements.Should().Contain("Contain at least one special character");
        requirements.Should().Contain("Contain at least 4 unique characters");
        requirements.Should().Contain("Not contain common weak patterns or sequential characters");
    }

    [Theory]
    [InlineData("!")]
    [InlineData("@")]
    [InlineData("#")]
    [InlineData("$")]
    [InlineData("%")]
    [InlineData("^")]
    [InlineData("&")]
    [InlineData("*")]
    [InlineData("(")]
    [InlineData(")")]
    [InlineData("_")]
    [InlineData("+")]
    [InlineData("-")]
    [InlineData("=")]
    [InlineData("[")]
    [InlineData("]")]
    [InlineData("{")]
    [InlineData("}")]
    [InlineData("|")]
    [InlineData(";")]
    [InlineData(":")]
    [InlineData(",")]
    [InlineData(".")]
    [InlineData("<")]
    [InlineData(">")]
    [InlineData("?")]
    public void ValidatePassword_AllSpecialCharacters_AcceptsValidSpecialChars(string specialChar)
    {
        // Arrange
        var password = $"TestPass1{specialChar}";

        // Act
        var result = _passwordValidationService.ValidatePassword(password);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void PasswordValidationResult_Success_CreatesValidResult()
    {
        // Act
        var result = PasswordValidationResult.Success();

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void PasswordValidationResult_Failed_CreatesValidResult()
    {
        // Arrange
        var errors = new[] { "Error 1", "Error 2" };

        // Act
        var result = PasswordValidationResult.Failed(errors);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().BeEquivalentTo(errors);
    }
}
