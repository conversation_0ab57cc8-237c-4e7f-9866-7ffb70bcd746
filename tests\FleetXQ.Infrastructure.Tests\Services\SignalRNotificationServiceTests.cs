using FleetXQ.Api.Hubs;
using FleetXQ.Application.Features.Alerts.DTOs;
using FleetXQ.Application.Features.Telemetry.EventHandlers;
using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Domain.Enums;
using FleetXQ.Infrastructure.Services;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace FleetXQ.Infrastructure.Tests.Services;

public class SignalRNotificationServiceTests
{
    private readonly Mock<IHubContext<TelemetryHub>> _mockTelemetryHubContext;
    private readonly Mock<IHubContext<AlertHub>> _mockAlertHubContext;
    private readonly Mock<IHubContext<DashboardHub>> _mockDashboardHubContext;
    private readonly Mock<ILogger<SignalRNotificationService>> _mockLogger;
    private readonly Mock<IHubCallerClients> _mockClients;
    private readonly Mock<IClientProxy> _mockClientProxy;
    private readonly SignalRNotificationService _service;

    public SignalRNotificationServiceTests()
    {
        _mockTelemetryHubContext = new Mock<IHubContext<TelemetryHub>>();
        _mockAlertHubContext = new Mock<IHubContext<AlertHub>>();
        _mockDashboardHubContext = new Mock<IHubContext<DashboardHub>>();
        _mockLogger = new Mock<ILogger<SignalRNotificationService>>();
        _mockClients = new Mock<IHubCallerClients>();
        _mockClientProxy = new Mock<IClientProxy>();

        // Setup common mock behavior
        SetupMockClients();

        _service = new SignalRNotificationService(
            _mockTelemetryHubContext.Object,
            _mockAlertHubContext.Object,
            _mockDashboardHubContext.Object,
            _mockLogger.Object);
    }

    private void SetupMockClients()
    {
        _mockTelemetryHubContext.Setup(h => h.Clients).Returns(_mockClients.Object);
        _mockAlertHubContext.Setup(h => h.Clients).Returns(_mockClients.Object);
        _mockDashboardHubContext.Setup(h => h.Clients).Returns(_mockClients.Object);

        _mockClients.Setup(c => c.Group(It.IsAny<string>())).Returns(_mockClientProxy.Object);
        _mockClients.Setup(c => c.All).Returns(_mockClientProxy.Object);

        _mockClientProxy.Setup(c => c.SendAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
    }

    [Fact]
    public async Task SendTelemetryUpdateAsync_WithValidData_ShouldSendToVehicleAndDashboardGroups()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var telemetryData = new TelemetryDataDto
        {
            VehicleId = vehicleId,
            Location = new LocationDto { Latitude = 40.7128m, Longitude = -74.0060m },
            SpeedKmh = 60,
            IsMoving = true,
            Timestamp = DateTime.UtcNow
        };

        // Act
        await _service.SendTelemetryUpdateAsync(vehicleId, telemetryData);

        // Assert
        _mockTelemetryHubContext.Verify(h => h.Clients.Group($"Vehicle_{vehicleId}")
            .SendAsync("TelemetryUpdate", telemetryData, It.IsAny<CancellationToken>()), Times.Once);

        _mockDashboardHubContext.Verify(h => h.Clients.Group("Dashboard_Fleet")
            .SendAsync("VehicleStatusUpdate", It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendTelemetryUpdateAsync_WithEmptyVehicleId_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.Empty;
        var telemetryData = new TelemetryDataDto();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.SendTelemetryUpdateAsync(vehicleId, telemetryData));
    }

    [Fact]
    public async Task SendTelemetryUpdateAsync_WithNullTelemetryData_ShouldThrowArgumentNullException()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.SendTelemetryUpdateAsync(vehicleId, null!));
    }

    [Fact]
    public async Task SendAlertNotificationAsync_WithValidAlert_ShouldSendToMultipleGroups()
    {
        // Arrange
        var alert = new AlertDto
        {
            Id = Guid.NewGuid(),
            Type = AlertType.Speed,
            Severity = AlertSeverity.Critical,
            VehicleId = Guid.NewGuid(),
            Description = "Speed violation detected",
            RequiresImmediateAttention = true
        };

        // Act
        await _service.SendAlertNotificationAsync(alert);

        // Assert
        // Should send to severity-specific group
        _mockAlertHubContext.Verify(h => h.Clients.Group($"Alerts_Severity_{alert.Severity}")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Once);

        // Should send to type-specific group
        _mockAlertHubContext.Verify(h => h.Clients.Group($"Alerts_Type_{alert.Type}")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Once);

        // Should send to vehicle-specific group
        _mockAlertHubContext.Verify(h => h.Clients.Group($"VehicleAlerts_{alert.VehicleId}")
            .SendAsync("VehicleAlert", alert, It.IsAny<CancellationToken>()), Times.Once);

        // Should send to dashboard
        _mockDashboardHubContext.Verify(h => h.Clients.Group("Dashboard_Fleet")
            .SendAsync("AlertUpdate", It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendAlertNotificationAsync_WithCriticalAlert_ShouldSendToAllRoleGroups()
    {
        // Arrange
        var alert = new AlertDto
        {
            Id = Guid.NewGuid(),
            Type = AlertType.Fuel,
            Severity = AlertSeverity.Critical,
            VehicleId = Guid.NewGuid(),
            Description = "Critical fuel level",
            RequiresImmediateAttention = true
        };

        // Act
        await _service.SendAlertNotificationAsync(alert);

        // Assert
        // Should send to admin group (all alerts)
        _mockAlertHubContext.Verify(h => h.Clients.Group("Alerts_Admin")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Once);

        // Should send to manager group (high and critical alerts)
        _mockAlertHubContext.Verify(h => h.Clients.Group("Alerts_Manager")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Once);

        // Should send to driver group (critical alerts only)
        _mockAlertHubContext.Verify(h => h.Clients.Group("Alerts_Driver")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendAlertNotificationAsync_WithHighAlert_ShouldSendToAdminAndManagerGroups()
    {
        // Arrange
        var alert = new AlertDto
        {
            Id = Guid.NewGuid(),
            Type = AlertType.Maintenance,
            Severity = AlertSeverity.High,
            Description = "Maintenance due soon"
        };

        // Act
        await _service.SendAlertNotificationAsync(alert);

        // Assert
        // Should send to admin group
        _mockAlertHubContext.Verify(h => h.Clients.Group("Alerts_Admin")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Once);

        // Should send to manager group
        _mockAlertHubContext.Verify(h => h.Clients.Group("Alerts_Manager")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Once);

        // Should NOT send to driver group (only critical alerts)
        _mockAlertHubContext.Verify(h => h.Clients.Group("Alerts_Driver")
            .SendAsync("AlertNotification", alert, It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task SendAlertNotificationAsync_WithNullAlert_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.SendAlertNotificationAsync(null!));
    }

    [Fact]
    public async Task SendVehicleStatusUpdateAsync_WithValidData_ShouldSendToDashboardGroups()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var status = "Active";
        var lastUpdate = DateTime.UtcNow;

        // Act
        await _service.SendVehicleStatusUpdateAsync(vehicleId, status, lastUpdate);

        // Assert
        _mockDashboardHubContext.Verify(h => h.Clients.Group("Dashboard_Fleet")
            .SendAsync("VehicleStatusUpdate", It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);

        _mockDashboardHubContext.Verify(h => h.Clients.Group("Dashboard_Vehicle")
            .SendAsync("VehicleStatusUpdate", It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendVehicleStatusUpdateAsync_WithEmptyVehicleId_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.Empty;
        var status = "Active";
        var lastUpdate = DateTime.UtcNow;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.SendVehicleStatusUpdateAsync(vehicleId, status, lastUpdate));
    }

    [Fact]
    public async Task SendVehicleStatusUpdateAsync_WithEmptyStatus_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var status = "";
        var lastUpdate = DateTime.UtcNow;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.SendVehicleStatusUpdateAsync(vehicleId, status, lastUpdate));
    }

    [Fact]
    public async Task SendDashboardMetricsUpdateAsync_WithValidMetrics_ShouldSendToAllDashboardGroups()
    {
        // Arrange
        var metrics = new { TotalVehicles = 10, ActiveVehicles = 8 };

        // Act
        await _service.SendDashboardMetricsUpdateAsync(metrics);

        // Assert
        _mockDashboardHubContext.Verify(h => h.Clients.Group("Dashboard_Fleet")
            .SendAsync("MetricsUpdate", metrics, It.IsAny<CancellationToken>()), Times.Once);

        _mockDashboardHubContext.Verify(h => h.Clients.Group("Dashboard_Analytics")
            .SendAsync("MetricsUpdate", metrics, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendDashboardMetricsUpdateAsync_WithNullMetrics_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.SendDashboardMetricsUpdateAsync(null!));
    }

    [Fact]
    public async Task SendToGroupAsync_WithVehicleGroup_ShouldUseTelemetryHubContext()
    {
        // Arrange
        var groupName = "Vehicle_123";
        var method = "TestMethod";
        var data = new { Message = "Test" };

        // Act
        await _service.SendToGroupAsync(groupName, method, data);

        // Assert
        _mockTelemetryHubContext.Verify(h => h.Clients.Group(groupName)
            .SendAsync(method, data, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendToGroupAsync_WithAlertGroup_ShouldUseAlertHubContext()
    {
        // Arrange
        var groupName = "Alerts_Critical";
        var method = "TestMethod";
        var data = new { Message = "Test" };

        // Act
        await _service.SendToGroupAsync(groupName, method, data);

        // Assert
        _mockAlertHubContext.Verify(h => h.Clients.Group(groupName)
            .SendAsync(method, data, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendToGroupAsync_WithDashboardGroup_ShouldUseDashboardHubContext()
    {
        // Arrange
        var groupName = "Dashboard_Fleet";
        var method = "TestMethod";
        var data = new { Message = "Test" };

        // Act
        await _service.SendToGroupAsync(groupName, method, data);

        // Assert
        _mockDashboardHubContext.Verify(h => h.Clients.Group(groupName)
            .SendAsync(method, data, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendToAllAsync_WithValidData_ShouldSendToAllHubContexts()
    {
        // Arrange
        var method = "GlobalMessage";
        var data = new { Message = "Global announcement" };

        // Act
        await _service.SendToAllAsync(method, data);

        // Assert
        _mockTelemetryHubContext.Verify(h => h.Clients.All
            .SendAsync(method, data, It.IsAny<CancellationToken>()), Times.Once);

        _mockAlertHubContext.Verify(h => h.Clients.All
            .SendAsync(method, data, It.IsAny<CancellationToken>()), Times.Once);

        _mockDashboardHubContext.Verify(h => h.Clients.All
            .SendAsync(method, data, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task SendToGroupAsync_WithInvalidGroupName_ShouldThrowArgumentException(string groupName)
    {
        // Arrange
        var method = "TestMethod";
        var data = new { Message = "Test" };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.SendToGroupAsync(groupName, method, data));
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task SendToAllAsync_WithInvalidMethod_ShouldThrowArgumentException(string method)
    {
        // Arrange
        var data = new { Message = "Test" };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.SendToAllAsync(method, data));
    }
}
