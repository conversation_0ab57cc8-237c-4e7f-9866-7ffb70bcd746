using FleetXQ.Application.Common.Interfaces;
using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Data;
using FleetXQ.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace FleetXQ.Api.Tests.Integration;

/// <summary>
/// Custom WebApplicationFactory for integration testing
/// </summary>
public class IntegrationTestWebApplicationFactory : WebApplicationFactory<Program>
{
    private readonly string _databaseName = Guid.NewGuid().ToString();

    /// <summary>
    /// Configures the web host for testing
    /// </summary>
    /// <param name="builder">The web host builder</param>
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseEnvironment("Testing");

        builder.ConfigureAppConfiguration((context, config) =>
        {
            // Add test configuration
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Jwt:Key"] = "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
                ["Jwt:Issuer"] = "FleetXQ",
                ["Jwt:Audience"] = "FleetXQ-Users",
                ["Jwt:ExpiryInMinutes"] = "60",
                ["Jwt:RefreshTokenExpiryInDays"] = "7",
                ["PasswordPolicy:MinLength"] = "8",
                ["PasswordPolicy:MaxLength"] = "128",
                ["PasswordPolicy:RequireUppercase"] = "true",
                ["PasswordPolicy:RequireLowercase"] = "true",
                ["PasswordPolicy:RequireDigit"] = "true",
                ["PasswordPolicy:RequireSpecialChar"] = "true",
                ["PasswordPolicy:MinUniqueChars"] = "4",
                ["RateLimit:Login:MaxRequests"] = "100",
                ["RateLimit:Login:TimeWindowMinutes"] = "15",
                ["RateLimit:Login:LockoutDurationMinutes"] = "30",
                ["RateLimit:Refresh:MaxRequests"] = "100",
                ["RateLimit:Refresh:TimeWindowMinutes"] = "5",
                ["RateLimit:Refresh:LockoutDurationMinutes"] = "15"
            });
        });

        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add in-memory database for testing
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseInMemoryDatabase(_databaseName);
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Ensure IApplicationDbContext is registered
            services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

            // Override SignalR services for testing
            services.AddSingleton<ISignalRConnectionManager, SignalRConnectionManager>();
            services.AddSingleton<SignalRConnectionStateService>();
            services.AddScoped<ISignalRNotificationService, SignalRNotificationService>();

            // Configure JWT authentication for testing
            services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = "FleetXQ",
                    ValidAudience = "FleetXQ-Users",
                    IssuerSigningKey = new SymmetricSecurityKey(
                        Encoding.UTF8.GetBytes("YourSuperSecretKeyThatIsAtLeast32CharactersLong!")),
                    ClockSkew = TimeSpan.Zero
                };
            });

            // Configure logging for testing
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Warning);
            });
        });

        builder.ConfigureLogging(logging =>
        {
            logging.ClearProviders();
            logging.AddConsole();
            logging.SetMinimumLevel(LogLevel.Warning);
        });
    }

    /// <summary>
    /// Creates a new database context for testing
    /// </summary>
    /// <returns>A new ApplicationDbContext instance</returns>
    public ApplicationDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(_databaseName)
            .EnableSensitiveDataLogging()
            .EnableDetailedErrors()
            .Options;

        return new ApplicationDbContext(options);
    }

    /// <summary>
    /// Seeds the database with test data
    /// </summary>
    /// <param name="seedAction">The action to seed the database</param>
    public async Task SeedDatabaseAsync(Func<ApplicationDbContext, Task> seedAction)
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        await context.Database.EnsureCreatedAsync();
        await seedAction(context);
        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Clears all data from the database
    /// </summary>
    public async Task ClearDatabaseAsync()
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Clear all entities
        context.Users.RemoveRange(context.Users);
        context.Vehicles.RemoveRange(context.Vehicles);
        context.Drivers.RemoveRange(context.Drivers);
        context.Alerts.RemoveRange(context.Alerts);
        context.Trips.RemoveRange(context.Trips);
        
        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Resets the database to a clean state
    /// </summary>
    public async Task ResetDatabaseAsync()
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        await context.Database.EnsureDeletedAsync();
        await context.Database.EnsureCreatedAsync();
    }

    /// <summary>
    /// Gets a service from the test container
    /// </summary>
    /// <typeparam name="T">The service type</typeparam>
    /// <returns>The service instance</returns>
    public T GetRequiredService<T>() where T : notnull
    {
        return Services.GetRequiredService<T>();
    }

    /// <summary>
    /// Creates a scoped service provider
    /// </summary>
    /// <returns>A new service scope</returns>
    public IServiceScope CreateScope()
    {
        return Services.CreateScope();
    }

    /// <summary>
    /// Disposes the factory and cleans up resources
    /// </summary>
    /// <param name="disposing">Whether disposing</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            // Clean up the in-memory database
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            context.Database.EnsureDeleted();
        }

        base.Dispose(disposing);
    }
}
