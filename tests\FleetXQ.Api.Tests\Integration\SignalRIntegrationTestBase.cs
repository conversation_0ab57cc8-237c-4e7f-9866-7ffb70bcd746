using FleetXQ.Api.Hubs;
using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Xunit;

namespace FleetXQ.Api.Tests.Integration;

/// <summary>
/// Base class for SignalR integration tests
/// </summary>
public abstract class SignalRIntegrationTestBase : IClassFixture<WebApplicationFactory<Program>>, IAsyncDisposable
{
    protected readonly WebApplicationFactory<Program> Factory;
    protected readonly HttpClient HttpClient;
    protected readonly string BaseUrl;
    
    protected SignalRIntegrationTestBase(WebApplicationFactory<Program> factory)
    {
        Factory = factory.WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");
            builder.ConfigureServices(services =>
            {
                // Override services for testing if needed
                services.AddSingleton<ISignalRConnectionManager, SignalRConnectionManager>();
                services.AddSingleton<SignalRConnectionStateService>();
                services.AddScoped<ISignalRNotificationService, SignalRNotificationService>();
            });
        });
        
        HttpClient = Factory.CreateClient();
        BaseUrl = HttpClient.BaseAddress!.ToString().TrimEnd('/');
    }

    /// <summary>
    /// Creates a SignalR connection to the specified hub
    /// </summary>
    protected async Task<HubConnection> CreateHubConnectionAsync(string hubPath, string? accessToken = null)
    {
        var connectionBuilder = new HubConnectionBuilder()
            .WithUrl($"{BaseUrl}{hubPath}", options =>
            {
                options.HttpMessageHandlerFactory = _ => Factory.Server.CreateHandler();
                
                if (!string.IsNullOrEmpty(accessToken))
                {
                    options.AccessTokenProvider = () => Task.FromResult(accessToken);
                }
            })
            .ConfigureLogging(logging =>
            {
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Debug);
            });

        var connection = connectionBuilder.Build();
        
        // Set up error handling
        connection.Closed += async (error) =>
        {
            if (error != null)
            {
                Console.WriteLine($"SignalR connection closed with error: {error.Message}");
            }
        };

        return connection;
    }

    /// <summary>
    /// Creates a test JWT token for authentication
    /// </summary>
    protected string CreateTestJwtToken(Guid userId, string username = "testuser", params string[] roles)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("YourSuperSecretKeyThatIsAtLeast32CharactersLong!"));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId.ToString()),
            new(ClaimTypes.Name, username),
            new(ClaimTypes.Email, $"{username}@test.com"),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        // Add roles
        var userRoles = roles.Any() ? roles : new[] { "User" };
        claims.AddRange(userRoles.Select(role => new Claim(ClaimTypes.Role, role)));

        var token = new JwtSecurityToken(
            issuer: "FleetXQ",
            audience: "FleetXQ-Users",
            claims: claims,
            expires: DateTime.UtcNow.AddHours(1),
            signingCredentials: credentials);

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    /// <summary>
    /// Waits for a SignalR method to be called with a timeout
    /// </summary>
    protected async Task<T> WaitForSignalRMethodAsync<T>(HubConnection connection, string methodName, TimeSpan? timeout = null)
    {
        var actualTimeout = timeout ?? TimeSpan.FromSeconds(10);
        var tcs = new TaskCompletionSource<T>();
        
        using var cts = new CancellationTokenSource(actualTimeout);
        cts.Token.Register(() => tcs.TrySetCanceled());

        connection.On<T>(methodName, data =>
        {
            tcs.TrySetResult(data);
        });

        try
        {
            return await tcs.Task;
        }
        catch (OperationCanceledException)
        {
            throw new TimeoutException($"Timeout waiting for SignalR method '{methodName}' after {actualTimeout}");
        }
    }

    /// <summary>
    /// Waits for a SignalR method to be called (without return value) with a timeout
    /// </summary>
    protected async Task WaitForSignalRMethodAsync(HubConnection connection, string methodName, TimeSpan? timeout = null)
    {
        var actualTimeout = timeout ?? TimeSpan.FromSeconds(10);
        var tcs = new TaskCompletionSource<bool>();
        
        using var cts = new CancellationTokenSource(actualTimeout);
        cts.Token.Register(() => tcs.TrySetCanceled());

        connection.On(methodName, () =>
        {
            tcs.TrySetResult(true);
        });

        try
        {
            await tcs.Task;
        }
        catch (OperationCanceledException)
        {
            throw new TimeoutException($"Timeout waiting for SignalR method '{methodName}' after {actualTimeout}");
        }
    }

    /// <summary>
    /// Asserts that a SignalR connection is in the expected state
    /// </summary>
    protected static void AssertConnectionState(HubConnection connection, HubConnectionState expectedState)
    {
        Assert.Equal(expectedState, connection.State);
    }

    /// <summary>
    /// Creates multiple test users with different roles
    /// </summary>
    protected (Guid UserId, string Token)[] CreateTestUsers(params string[] roles)
    {
        return roles.Select(role => 
        {
            var userId = Guid.NewGuid();
            var token = CreateTestJwtToken(userId, $"user_{role.ToLower()}", role);
            return (userId, token);
        }).ToArray();
    }

    /// <summary>
    /// Starts a SignalR connection and waits for it to be connected
    /// </summary>
    protected async Task<HubConnection> StartConnectionAsync(string hubPath, string? accessToken = null)
    {
        var connection = await CreateHubConnectionAsync(hubPath, accessToken);
        await connection.StartAsync();
        AssertConnectionState(connection, HubConnectionState.Connected);
        return connection;
    }

    /// <summary>
    /// Stops a SignalR connection gracefully
    /// </summary>
    protected async Task StopConnectionAsync(HubConnection connection)
    {
        if (connection.State == HubConnectionState.Connected)
        {
            await connection.StopAsync();
        }
        
        AssertConnectionState(connection, HubConnectionState.Disconnected);
    }

    /// <summary>
    /// Disposes all resources
    /// </summary>
    public virtual async ValueTask DisposeAsync()
    {
        HttpClient?.Dispose();
        
        if (Factory != null)
        {
            await Factory.DisposeAsync();
        }
    }
}

/// <summary>
/// Test data class for SignalR method calls
/// </summary>
public class SignalRMethodCall<T>
{
    public string MethodName { get; set; } = string.Empty;
    public T? Data { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Helper class for capturing SignalR method calls during tests
/// </summary>
public class SignalRMethodCapture<T>
{
    private readonly List<SignalRMethodCall<T>> _calls = new();
    private readonly object _lock = new();

    public void Capture(string methodName, T data)
    {
        lock (_lock)
        {
            _calls.Add(new SignalRMethodCall<T>
            {
                MethodName = methodName,
                Data = data,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    public List<SignalRMethodCall<T>> GetCalls()
    {
        lock (_lock)
        {
            return new List<SignalRMethodCall<T>>(_calls);
        }
    }

    public SignalRMethodCall<T>? GetLastCall()
    {
        lock (_lock)
        {
            return _calls.LastOrDefault();
        }
    }

    public int CallCount => _calls.Count;

    public void Clear()
    {
        lock (_lock)
        {
            _calls.Clear();
        }
    }
}
