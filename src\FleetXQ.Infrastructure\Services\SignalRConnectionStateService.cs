using FleetXQ.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FleetXQ.Infrastructure.Services;

/// <summary>
/// Service for managing SignalR connection state and lifecycle
/// </summary>
public class SignalRConnectionStateService
{
    private readonly ISignalRConnectionManager _connectionManager;
    private readonly ILogger<SignalRConnectionStateService> _logger;
    
    // Connection state tracking
    private readonly ConcurrentDictionary<string, ConnectionInfo> _connectionStates = new();
    private readonly ConcurrentDictionary<Guid, UserConnectionStats> _userStats = new();
    
    // Connection health monitoring
    private readonly Timer _healthCheckTimer;
    private readonly TimeSpan _healthCheckInterval = TimeSpan.FromMinutes(5);
    private readonly TimeSpan _connectionTimeout = TimeSpan.FromMinutes(30);

    public SignalRConnectionStateService(
        ISignalRConnectionManager connectionManager,
        ILogger<SignalRConnectionStateService> logger)
    {
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Start health check timer
        _healthCheckTimer = new Timer(PerformHealthCheck, null, _healthCheckInterval, _healthCheckInterval);
    }

    /// <summary>
    /// Records a new connection
    /// </summary>
    public async Task OnConnectionEstablishedAsync(string connectionId, Guid userId, string hubName, string userAgent, string ipAddress)
    {
        try
        {
            var connectionInfo = new ConnectionInfo
            {
                ConnectionId = connectionId,
                UserId = userId,
                HubName = hubName,
                UserAgent = userAgent,
                IpAddress = ipAddress,
                ConnectedAt = DateTime.UtcNow,
                LastActivity = DateTime.UtcNow,
                IsActive = true
            };

            _connectionStates[connectionId] = connectionInfo;
            
            // Update user statistics
            var userStats = _userStats.GetOrAdd(userId, _ => new UserConnectionStats { UserId = userId });
            userStats.TotalConnections++;
            userStats.ActiveConnections++;
            userStats.LastConnectionAt = DateTime.UtcNow;

            // Register with connection manager
            await _connectionManager.AddConnectionAsync(userId, connectionId);

            _logger.LogInformation("Connection established: {ConnectionId} for user {UserId} on {HubName} from {IpAddress}", 
                connectionId, userId, hubName, ipAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording connection establishment for {ConnectionId}", connectionId);
            throw;
        }
    }

    /// <summary>
    /// Records connection termination
    /// </summary>
    public async Task OnConnectionTerminatedAsync(string connectionId, Exception? exception = null)
    {
        try
        {
            if (_connectionStates.TryRemove(connectionId, out var connectionInfo))
            {
                connectionInfo.IsActive = false;
                connectionInfo.DisconnectedAt = DateTime.UtcNow;
                connectionInfo.DisconnectionReason = exception?.Message;

                // Update user statistics
                if (_userStats.TryGetValue(connectionInfo.UserId, out var userStats))
                {
                    userStats.ActiveConnections = Math.Max(0, userStats.ActiveConnections - 1);
                    userStats.LastDisconnectionAt = DateTime.UtcNow;
                    
                    if (exception != null)
                    {
                        userStats.ErrorDisconnections++;
                    }
                }

                // Unregister from connection manager
                await _connectionManager.RemoveConnectionAsync(connectionInfo.UserId, connectionId);

                var logLevel = exception != null ? LogLevel.Warning : LogLevel.Information;
                _logger.Log(logLevel, exception, 
                    "Connection terminated: {ConnectionId} for user {UserId} on {HubName}. Duration: {Duration}",
                    connectionId, connectionInfo.UserId, connectionInfo.HubName, 
                    connectionInfo.DisconnectedAt - connectionInfo.ConnectedAt);
            }
            else
            {
                _logger.LogWarning("Attempted to terminate unknown connection: {ConnectionId}", connectionId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording connection termination for {ConnectionId}", connectionId);
            throw;
        }
    }

    /// <summary>
    /// Updates the last activity time for a connection
    /// </summary>
    public void UpdateConnectionActivity(string connectionId)
    {
        if (_connectionStates.TryGetValue(connectionId, out var connectionInfo))
        {
            connectionInfo.LastActivity = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Gets connection information
    /// </summary>
    public ConnectionInfo? GetConnectionInfo(string connectionId)
    {
        _connectionStates.TryGetValue(connectionId, out var connectionInfo);
        return connectionInfo;
    }

    /// <summary>
    /// Gets user connection statistics
    /// </summary>
    public UserConnectionStats? GetUserStats(Guid userId)
    {
        _userStats.TryGetValue(userId, out var stats);
        return stats;
    }

    /// <summary>
    /// Gets overall connection statistics
    /// </summary>
    public ConnectionStatistics GetOverallStats()
    {
        var activeConnections = _connectionStates.Values.Count(c => c.IsActive);
        var totalUsers = _userStats.Count;
        var activeUsers = _userStats.Values.Count(u => u.ActiveConnections > 0);

        return new ConnectionStatistics
        {
            TotalActiveConnections = activeConnections,
            TotalUsers = totalUsers,
            ActiveUsers = activeUsers,
            AverageConnectionsPerUser = activeUsers > 0 ? (double)activeConnections / activeUsers : 0,
            ConnectionsByHub = _connectionStates.Values
                .Where(c => c.IsActive)
                .GroupBy(c => c.HubName)
                .ToDictionary(g => g.Key, g => g.Count()),
            LastUpdated = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Performs periodic health checks on connections
    /// </summary>
    private async void PerformHealthCheck(object? state)
    {
        try
        {
            var now = DateTime.UtcNow;
            var staleConnections = _connectionStates.Values
                .Where(c => c.IsActive && (now - c.LastActivity) > _connectionTimeout)
                .ToList();

            foreach (var staleConnection in staleConnections)
            {
                _logger.LogWarning("Detected stale connection: {ConnectionId} for user {UserId}. Last activity: {LastActivity}",
                    staleConnection.ConnectionId, staleConnection.UserId, staleConnection.LastActivity);

                // Mark as terminated due to timeout
                await OnConnectionTerminatedAsync(staleConnection.ConnectionId, 
                    new TimeoutException("Connection timed out due to inactivity"));
            }

            if (staleConnections.Any())
            {
                _logger.LogInformation("Health check completed. Cleaned up {Count} stale connections", staleConnections.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during connection health check");
        }
    }

    /// <summary>
    /// Disposes the service and stops the health check timer
    /// </summary>
    public void Dispose()
    {
        _healthCheckTimer?.Dispose();
    }
}

/// <summary>
/// Information about a SignalR connection
/// </summary>
public class ConnectionInfo
{
    public string ConnectionId { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string HubName { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public DateTime ConnectedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public DateTime? DisconnectedAt { get; set; }
    public string? DisconnectionReason { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Statistics for a user's connections
/// </summary>
public class UserConnectionStats
{
    public Guid UserId { get; set; }
    public int TotalConnections { get; set; }
    public int ActiveConnections { get; set; }
    public int ErrorDisconnections { get; set; }
    public DateTime? LastConnectionAt { get; set; }
    public DateTime? LastDisconnectionAt { get; set; }
}

/// <summary>
/// Overall connection statistics
/// </summary>
public class ConnectionStatistics
{
    public int TotalActiveConnections { get; set; }
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public double AverageConnectionsPerUser { get; set; }
    public Dictionary<string, int> ConnectionsByHub { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}
