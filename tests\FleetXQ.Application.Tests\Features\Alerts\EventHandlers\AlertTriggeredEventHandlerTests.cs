using FleetXQ.Application.Features.Alerts.EventHandlers;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Events;
using FleetXQ.Domain.ValueObjects;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace FleetXQ.Application.Tests.Features.Alerts.EventHandlers;

public class AlertTriggeredEventHandlerTests : TestBase
{
    private readonly AlertTriggeredEventHandler _handler;
    private readonly Mock<ILogger<AlertTriggeredEventHandler>> _mockLogger;

    public AlertTriggeredEventHandlerTests()
    {
        _mockLogger = CreateMockLogger<AlertTriggeredEventHandler>();
        _handler = new AlertTriggeredEventHandler(
            MockSignalRNotificationService.Object,
            MockVehicleRepository.Object,
            MockDriverRepository.Object,
            Mapper,
            _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidAlert_ShouldSendNotificationSuccessfully()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var alertId = Guid.NewGuid();
        var vehicle = CreateTestVehicle(vehicleId);
        var driver = CreateTestDriver();
        
        var alertEvent = new AlertTriggeredEvent(
            alertId,
            AlertType.SpeedingViolation,
            "Vehicle exceeding speed limit",
            vehicleId,
            driver.Id,
            AlertSeverity.High,
            DateTime.UtcNow);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);
        
        MockDriverRepository.Setup(x => x.GetByIdAsync(driver.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(driver);

        MockSignalRNotificationService.Setup(x => x.SendAlertNotificationAsync(
            It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(alertEvent, CreateCancellationToken());

        // Assert
        MockVehicleRepository.Verify(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()), Times.Once);
        MockDriverRepository.Verify(x => x.GetByIdAsync(driver.Id, It.IsAny<CancellationToken>()), Times.Once);
        MockSignalRNotificationService.Verify(x => x.SendAlertNotificationAsync(
            It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);
        
        VerifyLoggerCalled(_mockLogger, LogLevel.Information);
    }

    [Fact]
    public async Task Handle_WithNullVehicle_ShouldLogWarningAndContinue()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var alertId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        
        var alertEvent = new AlertTriggeredEvent(
            alertId,
            AlertType.LowFuel,
            "Low fuel level detected",
            vehicleId,
            driverId,
            AlertSeverity.Medium,
            DateTime.UtcNow);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        // Act
        await _handler.Handle(alertEvent, CreateCancellationToken());

        // Assert
        MockVehicleRepository.Verify(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()), Times.Once);
        MockDriverRepository.Verify(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Never);
        MockSignalRNotificationService.Verify(x => x.SendAlertNotificationAsync(
            It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Never);
        
        VerifyLoggerCalled(_mockLogger, LogLevel.Warning);
    }

    [Fact]
    public async Task Handle_WithNullDriver_ShouldLogWarningAndContinue()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var alertId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var vehicle = CreateTestVehicle(vehicleId);
        
        var alertEvent = new AlertTriggeredEvent(
            alertId,
            AlertType.MaintenanceRequired,
            "Maintenance required",
            vehicleId,
            driverId,
            AlertSeverity.Low,
            DateTime.UtcNow);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);
        
        MockDriverRepository.Setup(x => x.GetByIdAsync(driverId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Driver?)null);

        // Act
        await _handler.Handle(alertEvent, CreateCancellationToken());

        // Assert
        MockVehicleRepository.Verify(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()), Times.Once);
        MockDriverRepository.Verify(x => x.GetByIdAsync(driverId, It.IsAny<CancellationToken>()), Times.Once);
        MockSignalRNotificationService.Verify(x => x.SendAlertNotificationAsync(
            It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Never);
        
        VerifyLoggerCalled(_mockLogger, LogLevel.Warning);
    }

    [Fact]
    public async Task Handle_WithSignalRException_ShouldLogErrorAndNotThrow()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var alertId = Guid.NewGuid();
        var vehicle = CreateTestVehicle(vehicleId);
        var driver = CreateTestDriver();
        
        var alertEvent = new AlertTriggeredEvent(
            alertId,
            AlertType.GeofenceViolation,
            "Vehicle left authorized area",
            vehicleId,
            driver.Id,
            AlertSeverity.High,
            DateTime.UtcNow);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);
        
        MockDriverRepository.Setup(x => x.GetByIdAsync(driver.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(driver);

        MockSignalRNotificationService.Setup(x => x.SendAlertNotificationAsync(
            It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("SignalR connection failed"));

        // Act
        var act = async () => await _handler.Handle(alertEvent, CreateCancellationToken());

        // Assert
        await act.Should().NotThrowAsync();
        VerifyLoggerCalled(_mockLogger, LogLevel.Error);
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldLogErrorAndNotThrow()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var alertId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        
        var alertEvent = new AlertTriggeredEvent(
            alertId,
            AlertType.EngineOverheat,
            "Engine temperature too high",
            vehicleId,
            driverId,
            AlertSeverity.Critical,
            DateTime.UtcNow);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act
        var act = async () => await _handler.Handle(alertEvent, CreateCancellationToken());

        // Assert
        await act.Should().NotThrowAsync();
        VerifyLoggerCalled(_mockLogger, LogLevel.Error);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var alertId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        
        var alertEvent = new AlertTriggeredEvent(
            alertId,
            AlertType.SpeedingViolation,
            "Vehicle exceeding speed limit",
            vehicleId,
            driverId,
            AlertSeverity.High,
            DateTime.UtcNow);

        var cancellationToken = CreateCancelledCancellationToken();

        // Act & Assert
        var act = async () => await _handler.Handle(alertEvent, cancellationToken);
        await act.Should().ThrowAsync<OperationCanceledException>();
    }

    private static Vehicle CreateTestVehicle(Guid vehicleId)
    {
        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Truck", "Diesel");
        // Use reflection to set the ID since it's typically set by the database
        typeof(Vehicle).GetProperty("Id")?.SetValue(vehicle, vehicleId);
        return vehicle;
    }

    private static Driver CreateTestDriver()
    {
        return new Driver("John", "Doe", "<EMAIL>", "DL123456", DateTime.UtcNow.AddYears(-5));
    }
}
