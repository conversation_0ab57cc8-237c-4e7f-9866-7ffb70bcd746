using FleetXQ.Api.Models;
using FleetXQ.Api.Tests.Builders;
using FleetXQ.Application.Features.Drivers.Commands.CreateDriver;
using FleetXQ.Application.Features.Drivers.Commands.UpdateDriverStatus;
using FleetXQ.Application.Features.Drivers.DTOs;
using FleetXQ.Application.Features.Drivers.Queries.GetDriverById;
using FleetXQ.Application.Features.Drivers.Queries.GetDriverList;
using FleetXQ.Domain.Enums;
using System.Net;

namespace FleetXQ.Api.Tests.Integration;

/// <summary>
/// Integration tests for drivers endpoints
/// </summary>
public class DriversIntegrationTests : ApiIntegrationTestBase
{
    public DriversIntegrationTests(IntegrationTestWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task GetDrivers_WithValidAuthentication_ShouldReturnDriverList()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var drivers = TestDataBuilder.CreateDrivers(5).ToList();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
            context.Drivers.AddRange(drivers);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetDriverListQuery, PaginatedApiResponse<DriverListDto>>(
            "/api/drivers/list",
            new GetDriverListQuery { PageNumber = 1, PageSize = 10 }
        );

        // Assert
        response.IsSuccessStatusCode.Should().BeTrue();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Items.Should().HaveCount(5);
        content.Data.TotalCount.Should().Be(5);
    }

    [Fact]
    public async Task GetDriverById_WithExistingDriver_ShouldReturnDriver()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var driver = TestDataBuilder.CreateDriver(firstName: "John", lastName: "Doe", licenseNumber: "*********");

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
            context.Drivers.Add(driver);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetDriverByIdQuery, ApiResponse<GetDriverByIdResult>>(
            "/api/drivers/by-id",
            new GetDriverByIdQuery { DriverId = driver.Id }
        );

        // Assert
        AssertSuccessResponse(response, content);
        content!.Data.Should().NotBeNull();
        content.Data!.Driver.Should().NotBeNull();
        content.Data.Driver!.Id.Should().Be(driver.Id);
        content.Data.Driver.FirstName.Should().Be("John");
        content.Data.Driver.LastName.Should().Be("Doe");
        content.Data.Driver.LicenseNumber.Should().Be("*********");
    }

    [Fact]
    public async Task GetDriverById_WithNonExistentDriver_ShouldReturnNotFound()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var nonExistentId = Guid.NewGuid();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetDriverByIdQuery, ApiResponse<GetDriverByIdResult>>(
            "/api/drivers/by-id",
            new GetDriverByIdQuery { DriverId = nonExistentId }
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateDriver_WithValidData_ShouldCreateDriver()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(adminUser);
        });

        var token = CreateTestJwtToken(adminUser.Id, adminUser.Username, adminUser.Email, new[] { adminUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateDriverCommand
        {
            FirstName = "Jane",
            LastName = "Smith",
            LicenseNumber = "*********",
            PhoneNumber = "******-0123",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1985, 5, 15),
            HireDate = DateTime.UtcNow.Date
        };

        // Act
        var (response, content) = await PostAsync<CreateDriverCommand, ApiResponse<CreateDriverResult>>(
            "/api/drivers",
            createCommand
        );

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Driver.Should().NotBeNull();
        content.Data.Driver!.FirstName.Should().Be("Jane");
        content.Data.Driver.LastName.Should().Be("Smith");
        content.Data.Driver.LicenseNumber.Should().Be("*********");

        // Verify driver was actually created in database
        using var dbContext = GetDbContext();
        var createdDriver = await dbContext.Drivers.FindAsync(content.Data.Driver.Id);
        createdDriver.Should().NotBeNull();
        createdDriver!.FirstName.Should().Be("Jane");
    }

    [Fact]
    public async Task CreateDriver_WithDuplicateLicenseNumber_ShouldReturnBadRequest()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();
        var existingDriver = TestDataBuilder.CreateDriver(licenseNumber: "*********");

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(adminUser);
            context.Drivers.Add(existingDriver);
        });

        var token = CreateTestJwtToken(adminUser.Id, adminUser.Username, adminUser.Email, new[] { adminUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateDriverCommand
        {
            FirstName = "Another",
            LastName = "Driver",
            LicenseNumber = "*********", // Same license number
            PhoneNumber = "******-0456",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1990, 3, 20),
            HireDate = DateTime.UtcNow.Date
        };

        // Act
        var (response, content) = await PostAsync<CreateDriverCommand, ApiResponse<CreateDriverResult>>(
            "/api/drivers",
            createCommand
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.BadRequest);
        content!.Message.Should().Contain("license number");
    }

    [Fact]
    public async Task CreateDriver_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(adminUser);
        });

        var token = CreateTestJwtToken(adminUser.Id, adminUser.Username, adminUser.Email, new[] { adminUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateDriverCommand
        {
            FirstName = "", // Invalid: empty first name
            LastName = "Smith",
            LicenseNumber = "*********",
            PhoneNumber = "******-0123",
            Email = "invalid-email", // Invalid email format
            DateOfBirth = DateTime.UtcNow.AddYears(5), // Invalid: future date
            HireDate = DateTime.UtcNow.Date
        };

        // Act
        var (response, content) = await PostAsync<CreateDriverCommand, ApiResponse<CreateDriverResult>>(
            "/api/drivers",
            createCommand
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateDriver_WithUserRole_ShouldReturnForbidden()
    {
        // Arrange
        var regularUser = TestDataBuilder.CreateUser(role: UserRole.User);

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(regularUser);
        });

        var token = CreateTestJwtToken(regularUser.Id, regularUser.Username, regularUser.Email, new[] { regularUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var createCommand = new CreateDriverCommand
        {
            FirstName = "Test",
            LastName = "Driver",
            LicenseNumber = "*********",
            PhoneNumber = "******-0123",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1985, 5, 15),
            HireDate = DateTime.UtcNow.Date
        };

        // Act
        var (response, content) = await PostAsync<CreateDriverCommand, ApiResponse<CreateDriverResult>>(
            "/api/drivers",
            createCommand
        );

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task UpdateDriverStatus_WithValidData_ShouldUpdateStatus()
    {
        // Arrange
        var managerUser = TestDataBuilder.CreateManagerUser();
        var driver = TestDataBuilder.CreateDriver(status: DriverStatus.Available);

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(managerUser);
            context.Drivers.Add(driver);
        });

        var token = CreateTestJwtToken(managerUser.Id, managerUser.Username, managerUser.Email, new[] { managerUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var updateCommand = new UpdateDriverStatusCommand
        {
            DriverId = driver.Id,
            Status = DriverStatus.OnDuty
        };

        // Act
        var (response, content) = await PutAsync<UpdateDriverStatusCommand, ApiResponse<UpdateDriverStatusResult>>(
            "/api/drivers/status",
            updateCommand
        );

        // Assert
        AssertSuccessResponse(response, content);
        content!.Data.Should().NotBeNull();
        content.Data!.Driver.Should().NotBeNull();
        content.Data.Driver!.Status.Should().Be(DriverStatus.OnDuty);

        // Verify status was actually updated in database
        using var dbContext = GetDbContext();
        var updatedDriver = await dbContext.Drivers.FindAsync(driver.Id);
        updatedDriver.Should().NotBeNull();
        updatedDriver!.Status.Should().Be(DriverStatus.OnDuty);
    }

    [Fact]
    public async Task UpdateDriverStatus_WithNonExistentDriver_ShouldReturnNotFound()
    {
        // Arrange
        var managerUser = TestDataBuilder.CreateManagerUser();
        var nonExistentId = Guid.NewGuid();

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(managerUser);
        });

        var token = CreateTestJwtToken(managerUser.Id, managerUser.Username, managerUser.Email, new[] { managerUser.Role.ToString() });
        SetAuthorizationHeader(token);

        var updateCommand = new UpdateDriverStatusCommand
        {
            DriverId = nonExistentId,
            Status = DriverStatus.OnDuty
        };

        // Act
        var (response, content) = await PutAsync<UpdateDriverStatusCommand, ApiResponse<UpdateDriverStatusResult>>(
            "/api/drivers/status",
            updateCommand
        );

        // Assert
        AssertErrorResponse(response, content, HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetDrivers_WithoutAuthentication_ShouldReturnUnauthorized()
    {
        // Arrange
        ClearAuthorizationHeader();

        // Act
        var response = await HttpClient.GetAsync("/api/drivers");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task GetDrivers_WithStatusFilter_ShouldReturnFilteredResults()
    {
        // Arrange
        var user = TestDataBuilder.CreateUser();
        var availableDrivers = TestDataBuilder.CreateDrivers(3).ToList();
        var onDutyDrivers = TestDataBuilder.CreateDrivers(2).ToList();
        
        // Set different statuses
        foreach (var driver in onDutyDrivers)
        {
            driver.UpdateStatus(DriverStatus.OnDuty);
        }

        await SeedDatabaseAsync(async context =>
        {
            context.Users.Add(user);
            context.Drivers.AddRange(availableDrivers);
            context.Drivers.AddRange(onDutyDrivers);
        });

        var token = CreateTestJwtToken(user.Id, user.Username, user.Email, new[] { user.Role.ToString() });
        SetAuthorizationHeader(token);

        // Act
        var (response, content) = await PostAsync<GetDriverListQuery, PaginatedApiResponse<DriverListDto>>(
            "/api/drivers/list",
            new GetDriverListQuery 
            { 
                PageNumber = 1, 
                PageSize = 10,
                Status = DriverStatus.OnDuty
            }
        );

        // Assert
        response.IsSuccessStatusCode.Should().BeTrue();
        content.Should().NotBeNull();
        content!.Success.Should().BeTrue();
        content.Data.Should().NotBeNull();
        content.Data!.Items.Should().HaveCount(2);
        content.Data.Items.Should().OnlyContain(d => d.Status == DriverStatus.OnDuty);
    }
}
