using Microsoft.AspNetCore.Authorization;

namespace FleetXQ.Api.Authorization;

/// <summary>
/// Authorization requirement for SignalR hub access
/// </summary>
public class SignalRAuthorizationRequirement : IAuthorizationRequirement
{
    /// <summary>
    /// Gets the required roles for accessing the hub
    /// </summary>
    public IEnumerable<string> RequiredRoles { get; }

    /// <summary>
    /// Gets the hub name this requirement applies to
    /// </summary>
    public string HubName { get; }

    /// <summary>
    /// Gets a value indicating whether the user must be authenticated
    /// </summary>
    public bool RequireAuthentication { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="SignalRAuthorizationRequirement"/> class
    /// </summary>
    /// <param name="hubName">The hub name</param>
    /// <param name="requiredRoles">The required roles</param>
    /// <param name="requireAuthentication">Whether authentication is required</param>
    public SignalRAuthorizationRequirement(string hubName, IEnumerable<string>? requiredRoles = null, bool requireAuthentication = true)
    {
        HubName = hubName ?? throw new ArgumentNullException(nameof(hubName));
        RequiredRoles = requiredRoles ?? Enumerable.Empty<string>();
        RequireAuthentication = requireAuthentication;
    }
}

/// <summary>
/// Authorization handler for SignalR hub access
/// </summary>
public class SignalRAuthorizationHandler : AuthorizationHandler<SignalRAuthorizationRequirement>
{
    private readonly ILogger<SignalRAuthorizationHandler> _logger;

    public SignalRAuthorizationHandler(ILogger<SignalRAuthorizationHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context, 
        SignalRAuthorizationRequirement requirement)
    {
        try
        {
            // Check if authentication is required
            if (requirement.RequireAuthentication && !context.User.Identity!.IsAuthenticated)
            {
                _logger.LogWarning("Unauthenticated user attempted to access {HubName}", requirement.HubName);
                context.Fail();
                return Task.CompletedTask;
            }

            // If no specific roles are required, just check authentication
            if (!requirement.RequiredRoles.Any())
            {
                if (requirement.RequireAuthentication && context.User.Identity!.IsAuthenticated)
                {
                    context.Succeed(requirement);
                }
                else if (!requirement.RequireAuthentication)
                {
                    context.Succeed(requirement);
                }
                return Task.CompletedTask;
            }

            // Check if user has any of the required roles
            var userRoles = context.User.FindAll(System.Security.Claims.ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            var hasRequiredRole = requirement.RequiredRoles.Any(role => 
                userRoles.Contains(role, StringComparer.OrdinalIgnoreCase));

            if (hasRequiredRole)
            {
                _logger.LogDebug("User with roles [{UserRoles}] authorized for {HubName}", 
                    string.Join(", ", userRoles), requirement.HubName);
                context.Succeed(requirement);
            }
            else
            {
                _logger.LogWarning("User with roles [{UserRoles}] denied access to {HubName}. Required roles: [{RequiredRoles}]", 
                    string.Join(", ", userRoles), requirement.HubName, string.Join(", ", requirement.RequiredRoles));
                context.Fail();
            }

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during SignalR authorization for {HubName}", requirement.HubName);
            context.Fail();
            return Task.CompletedTask;
        }
    }
}
