using FleetXQ.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FleetXQ.Infrastructure.Services;

/// <summary>
/// In-memory implementation of SignalR connection manager
/// </summary>
public class SignalRConnectionManager : ISignalRConnectionManager
{
    private readonly ILogger<SignalRConnectionManager> _logger;
    
    // Thread-safe collections for managing connections
    private readonly ConcurrentDictionary<Guid, HashSet<string>> _userConnections = new();
    private readonly ConcurrentDictionary<string, Guid> _connectionUsers = new();
    private readonly ConcurrentDictionary<string, HashSet<Guid>> _groupUsers = new();
    private readonly ConcurrentDictionary<Guid, HashSet<string>> _userGroups = new();
    
    // Locks for thread-safe operations on HashSets
    private readonly ConcurrentDictionary<Guid, object> _userLocks = new();
    private readonly ConcurrentDictionary<string, object> _groupLocks = new();

    public SignalRConnectionManager(ILogger<SignalRConnectionManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public Task AddConnectionAsync(Guid userId, string connectionId)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));
        
        if (string.IsNullOrWhiteSpace(connectionId))
            throw new ArgumentException("Connection ID cannot be empty", nameof(connectionId));

        var userLock = _userLocks.GetOrAdd(userId, _ => new object());
        
        lock (userLock)
        {
            var connections = _userConnections.GetOrAdd(userId, _ => new HashSet<string>());
            connections.Add(connectionId);
            _connectionUsers[connectionId] = userId;
        }

        _logger.LogDebug("Added connection {ConnectionId} for user {UserId}", connectionId, userId);
        return Task.CompletedTask;
    }

    public Task RemoveConnectionAsync(Guid userId, string connectionId)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));
        
        if (string.IsNullOrWhiteSpace(connectionId))
            throw new ArgumentException("Connection ID cannot be empty", nameof(connectionId));

        var userLock = _userLocks.GetOrAdd(userId, _ => new object());
        
        lock (userLock)
        {
            if (_userConnections.TryGetValue(userId, out var connections))
            {
                connections.Remove(connectionId);
                if (connections.Count == 0)
                {
                    _userConnections.TryRemove(userId, out _);
                    _userLocks.TryRemove(userId, out _);
                }
            }
            
            _connectionUsers.TryRemove(connectionId, out _);
        }

        _logger.LogDebug("Removed connection {ConnectionId} for user {UserId}", connectionId, userId);
        return Task.CompletedTask;
    }

    public Task<IEnumerable<string>> GetConnectionsAsync(Guid userId)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        if (_userConnections.TryGetValue(userId, out var connections))
        {
            var userLock = _userLocks.GetOrAdd(userId, _ => new object());
            lock (userLock)
            {
                return Task.FromResult<IEnumerable<string>>(connections.ToList());
            }
        }

        return Task.FromResult<IEnumerable<string>>(Enumerable.Empty<string>());
    }

    public Task<Guid?> GetUserIdAsync(string connectionId)
    {
        if (string.IsNullOrWhiteSpace(connectionId))
            throw new ArgumentException("Connection ID cannot be empty", nameof(connectionId));

        _connectionUsers.TryGetValue(connectionId, out var userId);
        return Task.FromResult<Guid?>(userId == Guid.Empty ? null : userId);
    }

    public Task<IEnumerable<Guid>> GetGroupUsersAsync(string groupName)
    {
        if (string.IsNullOrWhiteSpace(groupName))
            throw new ArgumentException("Group name cannot be empty", nameof(groupName));

        if (_groupUsers.TryGetValue(groupName, out var users))
        {
            var groupLock = _groupLocks.GetOrAdd(groupName, _ => new object());
            lock (groupLock)
            {
                return Task.FromResult<IEnumerable<Guid>>(users.ToList());
            }
        }

        return Task.FromResult<IEnumerable<Guid>>(Enumerable.Empty<Guid>());
    }

    public Task AddToGroupAsync(Guid userId, string groupName)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));
        
        if (string.IsNullOrWhiteSpace(groupName))
            throw new ArgumentException("Group name cannot be empty", nameof(groupName));

        var groupLock = _groupLocks.GetOrAdd(groupName, _ => new object());
        var userLock = _userLocks.GetOrAdd(userId, _ => new object());

        // Use consistent locking order to prevent deadlocks
        var locks = new[] { groupLock, userLock }.OrderBy(l => l.GetHashCode()).ToArray();
        
        lock (locks[0])
        {
            lock (locks[1])
            {
                var groupUsers = _groupUsers.GetOrAdd(groupName, _ => new HashSet<Guid>());
                groupUsers.Add(userId);

                var userGroups = _userGroups.GetOrAdd(userId, _ => new HashSet<string>());
                userGroups.Add(groupName);
            }
        }

        _logger.LogDebug("Added user {UserId} to group {GroupName}", userId, groupName);
        return Task.CompletedTask;
    }

    public Task RemoveFromGroupAsync(Guid userId, string groupName)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));
        
        if (string.IsNullOrWhiteSpace(groupName))
            throw new ArgumentException("Group name cannot be empty", nameof(groupName));

        var groupLock = _groupLocks.GetOrAdd(groupName, _ => new object());
        var userLock = _userLocks.GetOrAdd(userId, _ => new object());

        // Use consistent locking order to prevent deadlocks
        var locks = new[] { groupLock, userLock }.OrderBy(l => l.GetHashCode()).ToArray();
        
        lock (locks[0])
        {
            lock (locks[1])
            {
                if (_groupUsers.TryGetValue(groupName, out var groupUsers))
                {
                    groupUsers.Remove(userId);
                    if (groupUsers.Count == 0)
                    {
                        _groupUsers.TryRemove(groupName, out _);
                        _groupLocks.TryRemove(groupName, out _);
                    }
                }

                if (_userGroups.TryGetValue(userId, out var userGroups))
                {
                    userGroups.Remove(groupName);
                    if (userGroups.Count == 0)
                    {
                        _userGroups.TryRemove(userId, out _);
                    }
                }
            }
        }

        _logger.LogDebug("Removed user {UserId} from group {GroupName}", userId, groupName);
        return Task.CompletedTask;
    }

    public Task<IEnumerable<string>> GetUserGroupsAsync(Guid userId)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        if (_userGroups.TryGetValue(userId, out var groups))
        {
            var userLock = _userLocks.GetOrAdd(userId, _ => new object());
            lock (userLock)
            {
                return Task.FromResult<IEnumerable<string>>(groups.ToList());
            }
        }

        return Task.FromResult<IEnumerable<string>>(Enumerable.Empty<string>());
    }

    public Task<bool> IsUserConnectedAsync(Guid userId)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        var isConnected = _userConnections.ContainsKey(userId) && _userConnections[userId].Count > 0;
        return Task.FromResult(isConnected);
    }

    public Task<int> GetConnectionCountAsync()
    {
        var count = _connectionUsers.Count;
        return Task.FromResult(count);
    }
}
