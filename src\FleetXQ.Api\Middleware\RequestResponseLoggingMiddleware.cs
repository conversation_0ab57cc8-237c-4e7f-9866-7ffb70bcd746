using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Serilog;
using Serilog.Context;
using System.Diagnostics;
using System.Security.Claims;
using System.Text;

namespace FleetXQ.Api.Middleware;

/// <summary>
/// Middleware for comprehensive request/response logging with correlation tracking
/// </summary>
public class RequestResponseLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestResponseLoggingMiddleware> _logger;
    private readonly TelemetryClient _telemetryClient;
    private readonly IConfiguration _configuration;
    private static readonly string[] SensitiveHeaders = { "authorization", "cookie", "x-api-key", "x-auth-token" };
    private static readonly string[] SensitiveQueryParams = { "password", "token", "key", "secret" };

    public RequestResponseLoggingMiddleware(
        RequestDelegate next,
        ILogger<RequestResponseLoggingMiddleware> logger,
        TelemetryClient telemetryClient,
        IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _telemetryClient = telemetryClient;
        _configuration = configuration;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var correlationId = GetOrCreateCorrelationId(context);
        var stopwatch = Stopwatch.StartNew();
        var requestStartTime = DateTime.UtcNow;

        // Add correlation ID to log context
        using (LogContext.PushProperty("CorrelationId", correlationId))
        using (LogContext.PushProperty("RequestId", context.TraceIdentifier))
        using (LogContext.PushProperty("RequestPath", context.Request.Path))
        using (LogContext.PushProperty("RequestMethod", context.Request.Method))
        {
            await LogRequestAsync(context, correlationId, requestStartTime);

            // Capture original response body stream
            var originalBodyStream = context.Response.Body;
            using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;

            Exception? exception = null;
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                exception = ex;
                throw;
            }
            finally
            {
                stopwatch.Stop();
                
                // Copy response body back to original stream
                responseBodyStream.Seek(0, SeekOrigin.Begin);
                await responseBodyStream.CopyToAsync(originalBodyStream);
                
                await LogResponseAsync(context, correlationId, stopwatch.ElapsedMilliseconds, 
                    requestStartTime, responseBodyStream, exception);
            }
        }
    }

    private async Task LogRequestAsync(HttpContext context, string correlationId, DateTime requestStartTime)
    {
        var request = context.Request;
        var user = context.User;
        
        // Extract user information
        var userId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userName = user?.FindFirst(ClaimTypes.Name)?.Value ?? user?.FindFirst("name")?.Value;
        var userEmail = user?.FindFirst(ClaimTypes.Email)?.Value;
        var userRoles = user?.FindAll(ClaimTypes.Role)?.Select(c => c.Value).ToArray();

        // Add user context to log properties
        using (LogContext.PushProperty("UserId", userId))
        using (LogContext.PushProperty("UserName", userName))
        using (LogContext.PushProperty("UserEmail", userEmail))
        using (LogContext.PushProperty("UserRoles", userRoles))
        using (LogContext.PushProperty("ClientIp", GetClientIpAddress(context)))
        using (LogContext.PushProperty("UserAgent", request.Headers.UserAgent.ToString()))
        {
            var requestInfo = new
            {
                CorrelationId = correlationId,
                RequestId = context.TraceIdentifier,
                Method = request.Method,
                Path = request.Path.Value,
                QueryString = SanitizeQueryString(request.QueryString.Value),
                Headers = SanitizeHeaders(request.Headers),
                ContentType = request.ContentType,
                ContentLength = request.ContentLength,
                Protocol = request.Protocol,
                Scheme = request.Scheme,
                Host = request.Host.Value,
                ClientIp = GetClientIpAddress(context),
                UserAgent = request.Headers.UserAgent.ToString(),
                User = new
                {
                    Id = userId,
                    Name = userName,
                    Email = userEmail,
                    Roles = userRoles,
                    IsAuthenticated = user?.Identity?.IsAuthenticated ?? false
                },
                Timestamp = requestStartTime
            };

            _logger.LogInformation("HTTP Request Started: {Method} {Path} from {ClientIp} by {UserName} ({UserId})",
                request.Method, request.Path, GetClientIpAddress(context), userName ?? "Anonymous", userId ?? "N/A");

            // Log detailed request information at debug level
            _logger.LogDebug("Request Details: {@RequestInfo}", requestInfo);

            // Send custom telemetry to Application Insights
            var requestTelemetry = new RequestTelemetry
            {
                Name = $"{request.Method} {request.Path}",
                Url = new Uri($"{request.Scheme}://{request.Host}{request.Path}{request.QueryString}"),
                Timestamp = requestStartTime,
                Context = { User = { Id = userId, AuthenticatedUserId = userId } }
            };

            requestTelemetry.Properties["CorrelationId"] = correlationId;
            requestTelemetry.Properties["UserName"] = userName ?? "Anonymous";
            requestTelemetry.Properties["ClientIp"] = GetClientIpAddress(context);
            requestTelemetry.Properties["UserAgent"] = request.Headers.UserAgent.ToString();

            _telemetryClient.TrackRequest(requestTelemetry);

            // Log request body for POST/PUT requests (with size limit and sensitive data filtering)
            if (ShouldLogRequestBody(request))
            {
                await LogRequestBodyAsync(request, correlationId);
            }
        }
    }

    private async Task LogResponseAsync(HttpContext context, string correlationId, long elapsedMs, 
        DateTime requestStartTime, MemoryStream responseBodyStream, Exception? exception)
    {
        var response = context.Response;
        var request = context.Request;
        
        var responseInfo = new
        {
            CorrelationId = correlationId,
            RequestId = context.TraceIdentifier,
            StatusCode = response.StatusCode,
            ContentType = response.ContentType,
            ContentLength = response.ContentLength ?? responseBodyStream.Length,
            Headers = SanitizeHeaders(response.Headers.ToDictionary(h => h.Key, h => h.Value.AsEnumerable())),
            ElapsedMilliseconds = elapsedMs,
            Success = exception == null && response.StatusCode < 400,
            Timestamp = DateTime.UtcNow
        };

        var logLevel = DetermineLogLevel(response.StatusCode, elapsedMs, exception);
        var statusCategory = GetStatusCategory(response.StatusCode);

        _logger.Log(logLevel, 
            "HTTP Request {Status}: {Method} {Path} responded {StatusCode} in {ElapsedMs}ms",
            statusCategory, request.Method, request.Path, response.StatusCode, elapsedMs);

        // Log detailed response information at debug level
        _logger.LogDebug("Response Details: {@ResponseInfo}", responseInfo);

        // Log response body for errors or debug level
        if (ShouldLogResponseBody(response, exception))
        {
            await LogResponseBodyAsync(responseBodyStream, correlationId, response.StatusCode);
        }

        // Track performance metrics
        TrackPerformanceMetrics(request, response, elapsedMs, exception);

        // Send completion telemetry to Application Insights
        var requestTelemetry = new RequestTelemetry
        {
            Name = $"{request.Method} {request.Path}",
            Duration = TimeSpan.FromMilliseconds(elapsedMs),
            ResponseCode = response.StatusCode.ToString(),
            Success = exception == null && response.StatusCode < 400,
            Timestamp = requestStartTime
        };

        requestTelemetry.Properties["CorrelationId"] = correlationId;
        requestTelemetry.Properties["ElapsedMs"] = elapsedMs.ToString();
        
        if (exception != null)
        {
            requestTelemetry.Properties["Exception"] = exception.GetType().Name;
            requestTelemetry.Properties["ExceptionMessage"] = exception.Message;
        }

        _telemetryClient.TrackRequest(requestTelemetry);

        // Track custom metrics
        _telemetryClient.TrackMetric($"Request.Duration.{request.Method}", elapsedMs);
        _telemetryClient.TrackMetric($"Request.StatusCode.{response.StatusCode}", 1);
        
        if (elapsedMs > 5000) // Log slow requests
        {
            _logger.LogWarning("Slow Request Detected: {Method} {Path} took {ElapsedMs}ms", 
                request.Method, request.Path, elapsedMs);
            _telemetryClient.TrackMetric("Request.Slow", 1);
        }
    }

    private string GetOrCreateCorrelationId(HttpContext context)
    {
        // Try to get correlation ID from headers
        if (context.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId))
        {
            return correlationId.FirstOrDefault() ?? Guid.NewGuid().ToString();
        }

        // Generate new correlation ID
        var newCorrelationId = Guid.NewGuid().ToString();
        context.Response.Headers.Add("X-Correlation-ID", newCorrelationId);
        return newCorrelationId;
    }

    private string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private Dictionary<string, object> SanitizeHeaders(IHeaderDictionary headers)
    {
        return headers.ToDictionary(
            h => h.Key,
            h => SensitiveHeaders.Contains(h.Key.ToLowerInvariant()) ? "[REDACTED]" : (object)h.Value.ToString()
        );
    }

    private Dictionary<string, object> SanitizeHeaders(Dictionary<string, IEnumerable<string>> headers)
    {
        return headers.ToDictionary(
            h => h.Key,
            h => SensitiveHeaders.Contains(h.Key.ToLowerInvariant()) ? "[REDACTED]" : (object)string.Join(", ", h.Value)
        );
    }

    private string? SanitizeQueryString(string? queryString)
    {
        if (string.IsNullOrEmpty(queryString))
            return queryString;

        var sanitized = new StringBuilder();
        var pairs = queryString.TrimStart('?').Split('&');
        
        for (int i = 0; i < pairs.Length; i++)
        {
            var pair = pairs[i];
            var keyValue = pair.Split('=');
            
            if (keyValue.Length == 2)
            {
                var key = keyValue[0].ToLowerInvariant();
                var value = SensitiveQueryParams.Contains(key) ? "[REDACTED]" : keyValue[1];
                sanitized.Append($"{keyValue[0]}={value}");
            }
            else
            {
                sanitized.Append(pair);
            }
            
            if (i < pairs.Length - 1)
                sanitized.Append('&');
        }
        
        return sanitized.ToString();
    }

    private bool ShouldLogRequestBody(HttpRequest request)
    {
        return (request.Method == "POST" || request.Method == "PUT" || request.Method == "PATCH") &&
               request.ContentLength.HasValue &&
               request.ContentLength.Value > 0 &&
               request.ContentLength.Value < 10240 && // 10KB limit
               request.ContentType?.Contains("application/json") == true;
    }

    private async Task LogRequestBodyAsync(HttpRequest request, string correlationId)
    {
        try
        {
            request.EnableBuffering();
            request.Body.Position = 0;
            
            using var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true);
            var body = await reader.ReadToEndAsync();
            request.Body.Position = 0;

            if (!string.IsNullOrWhiteSpace(body))
            {
                _logger.LogDebug("Request Body for {CorrelationId}: {RequestBody}", correlationId, body);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log request body for {CorrelationId}", correlationId);
        }
    }

    private bool ShouldLogResponseBody(HttpResponse response, Exception? exception)
    {
        return exception != null || 
               response.StatusCode >= 400 || 
               _logger.IsEnabled(LogLevel.Debug);
    }

    private async Task LogResponseBodyAsync(MemoryStream responseBodyStream, string correlationId, int statusCode)
    {
        try
        {
            if (responseBodyStream.Length > 0 && responseBodyStream.Length < 10240) // 10KB limit
            {
                responseBodyStream.Seek(0, SeekOrigin.Begin);
                using var reader = new StreamReader(responseBodyStream, Encoding.UTF8, leaveOpen: true);
                var body = await reader.ReadToEndAsync();
                
                if (!string.IsNullOrWhiteSpace(body))
                {
                    var logLevel = statusCode >= 400 ? LogLevel.Warning : LogLevel.Debug;
                    _logger.Log(logLevel, "Response Body for {CorrelationId}: {ResponseBody}", correlationId, body);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log response body for {CorrelationId}", correlationId);
        }
    }

    private LogLevel DetermineLogLevel(int statusCode, long elapsedMs, Exception? exception)
    {
        if (exception != null || statusCode >= 500)
            return LogLevel.Error;
        
        if (statusCode >= 400)
            return LogLevel.Warning;
        
        if (elapsedMs > 5000) // Slow requests
            return LogLevel.Warning;
        
        return LogLevel.Information;
    }

    private string GetStatusCategory(int statusCode)
    {
        return statusCode switch
        {
            >= 200 and < 300 => "Completed",
            >= 300 and < 400 => "Redirected",
            >= 400 and < 500 => "Client Error",
            >= 500 => "Server Error",
            _ => "Unknown"
        };
    }

    private void TrackPerformanceMetrics(HttpRequest request, HttpResponse response, long elapsedMs, Exception? exception)
    {
        // Track endpoint-specific metrics
        var endpoint = $"{request.Method}_{request.Path.Value?.Replace("/", "_")}";
        _telemetryClient.TrackMetric($"Endpoint.{endpoint}.Duration", elapsedMs);
        _telemetryClient.TrackMetric($"Endpoint.{endpoint}.Requests", 1);
        
        if (exception != null)
        {
            _telemetryClient.TrackMetric($"Endpoint.{endpoint}.Errors", 1);
        }
        
        // Track status code distribution
        _telemetryClient.TrackMetric($"StatusCode.{response.StatusCode}", 1);
        
        // Track performance buckets
        var performanceBucket = elapsedMs switch
        {
            < 100 => "Fast",
            < 500 => "Normal",
            < 2000 => "Slow",
            _ => "VerySlow"
        };
        
        _telemetryClient.TrackMetric($"Performance.{performanceBucket}", 1);
    }
}
