using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Infrastructure.Authentication;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Xunit;

namespace FleetXQ.Application.Tests.Infrastructure;

/// <summary>
/// Unit tests for TokenService
/// </summary>
public sealed class TokenServiceTests
{
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<TokenService>> _mockLogger;
    private readonly TokenService _tokenService;

    public TokenServiceTests()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<TokenService>>();

        // Setup configuration
        _mockConfiguration.Setup(x => x["Jwt:Key"]).Returns("YourSuperSecretKeyThatIsAtLeast32CharactersLong!");
        _mockConfiguration.Setup(x => x["Jwt:Issuer"]).Returns("FleetXQ");
        _mockConfiguration.Setup(x => x["Jwt:Audience"]).Returns("FleetXQ-Users");
        _mockConfiguration.Setup(x => x.GetValue<int>("Jwt:ExpiryInMinutes", 60)).Returns(60);
        _mockConfiguration.Setup(x => x.GetValue<int>("Jwt:RefreshTokenExpiryInDays", 7)).Returns(7);

        _tokenService = new TokenService(_mockConfiguration.Object, _mockLogger.Object);
    }

    [Fact]
    public void GenerateAccessToken_ValidUser_ReturnsValidJwtToken()
    {
        // Arrange
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.User);
        var userId = Guid.NewGuid();
        typeof(User).GetProperty("Id")?.SetValue(user, userId);

        // Act
        var token = _tokenService.GenerateAccessToken(user);

        // Assert
        token.Should().NotBeNullOrEmpty();

        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.NameIdentifier && c.Value == userId.ToString());
        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.Name && c.Value == "testuser");
        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.Email && c.Value == "<EMAIL>");
        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.Role && c.Value == "User");
        jsonToken.Claims.Should().Contain(c => c.Type == "full_name" && c.Value == "John Doe");
        jsonToken.Claims.Should().Contain(c => c.Type == "is_active" && c.Value == "True");
        jsonToken.Claims.Should().Contain(c => c.Type == "jti");

        jsonToken.Issuer.Should().Be("FleetXQ");
        jsonToken.Audiences.Should().Contain("FleetXQ-Users");
    }

    [Fact]
    public void GenerateAccessToken_NullUser_ThrowsArgumentNullException()
    {
        // Act & Assert
        var action = () => _tokenService.GenerateAccessToken(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void GenerateRefreshToken_ReturnsBase64String()
    {
        // Act
        var refreshToken = _tokenService.GenerateRefreshToken();

        // Assert
        refreshToken.Should().NotBeNullOrEmpty();
        
        // Should be valid base64
        var action = () => Convert.FromBase64String(refreshToken);
        action.Should().NotThrow();

        // Should be at least 64 bytes when decoded
        var bytes = Convert.FromBase64String(refreshToken);
        bytes.Length.Should().BeGreaterOrEqualTo(64);
    }

    [Fact]
    public void GenerateSecureRefreshToken_ValidUserId_ReturnsSecureToken()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act
        var refreshToken = _tokenService.GenerateSecureRefreshToken(userId);

        // Assert
        refreshToken.Should().NotBeNullOrEmpty();
        
        // Should be valid base64
        var bytes = Convert.FromBase64String(refreshToken);
        bytes.Length.Should().BeGreaterThan(64); // Should be larger due to additional entropy
    }

    [Fact]
    public void ValidateToken_ValidToken_ReturnsTrue()
    {
        // Arrange
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.User);
        var token = _tokenService.GenerateAccessToken(user);

        // Act
        var isValid = _tokenService.ValidateToken(token);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void ValidateToken_InvalidToken_ReturnsFalse()
    {
        // Arrange
        var invalidToken = "invalid.token.here";

        // Act
        var isValid = _tokenService.ValidateToken(invalidToken);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void ValidateToken_EmptyToken_ReturnsFalse()
    {
        // Act
        var isValid = _tokenService.ValidateToken(string.Empty);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void GetUserIdFromToken_ValidToken_ReturnsUserId()
    {
        // Arrange
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.User);
        var userId = Guid.NewGuid();
        typeof(User).GetProperty("Id")?.SetValue(user, userId);
        var token = _tokenService.GenerateAccessToken(user);

        // Act
        var extractedUserId = _tokenService.GetUserIdFromToken(token);

        // Assert
        extractedUserId.Should().Be(userId);
    }

    [Fact]
    public void GetUserIdFromToken_InvalidToken_ReturnsNull()
    {
        // Arrange
        var invalidToken = "invalid.token.here";

        // Act
        var userId = _tokenService.GetUserIdFromToken(invalidToken);

        // Assert
        userId.Should().BeNull();
    }

    [Fact]
    public void GetTokenExpiry_ValidToken_ReturnsExpiryTime()
    {
        // Arrange
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", UserRole.User);
        var token = _tokenService.GenerateAccessToken(user);

        // Act
        var expiry = _tokenService.GetTokenExpiry(token);

        // Assert
        expiry.Should().NotBeNull();
        expiry.Should().BeAfter(DateTime.UtcNow);
        expiry.Should().BeBefore(DateTime.UtcNow.AddHours(2)); // Should expire within 2 hours
    }

    [Fact]
    public void GetTokenExpiry_InvalidToken_ReturnsNull()
    {
        // Arrange
        var invalidToken = "invalid.token.here";

        // Act
        var expiry = _tokenService.GetTokenExpiry(invalidToken);

        // Assert
        expiry.Should().BeNull();
    }

    [Fact]
    public void GetAccessTokenExpiry_ReturnsConfiguredExpiry()
    {
        // Act
        var expiry = _tokenService.GetAccessTokenExpiry();

        // Assert
        expiry.Should().Be(TimeSpan.FromMinutes(60));
    }

    [Fact]
    public void GetRefreshTokenExpiry_ReturnsConfiguredExpiry()
    {
        // Act
        var expiry = _tokenService.GetRefreshTokenExpiry();

        // Assert
        expiry.Should().Be(TimeSpan.FromDays(7));
    }

    [Fact]
    public void IsValidRefreshTokenFormat_ValidToken_ReturnsTrue()
    {
        // Arrange
        var refreshToken = _tokenService.GenerateRefreshToken();

        // Act
        var isValid = _tokenService.IsValidRefreshTokenFormat(refreshToken);

        // Assert
        isValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("invalid-token")]
    [InlineData("dG9vLXNob3J0")] // "too-short" in base64, but less than 32 bytes
    public void IsValidRefreshTokenFormat_InvalidToken_ReturnsFalse(string invalidToken)
    {
        // Act
        var isValid = _tokenService.IsValidRefreshTokenFormat(invalidToken);

        // Assert
        isValid.Should().BeFalse();
    }

    [Theory]
    [InlineData(UserRole.Admin)]
    [InlineData(UserRole.Manager)]
    [InlineData(UserRole.Driver)]
    [InlineData(UserRole.User)]
    public void GenerateAccessToken_DifferentRoles_IncludesCorrectRoleClaim(UserRole role)
    {
        // Arrange
        var user = new User("testuser", "<EMAIL>", "hashedpassword", "John", "Doe", role);

        // Act
        var token = _tokenService.GenerateAccessToken(user);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.Role && c.Value == role.ToString());
    }
}
