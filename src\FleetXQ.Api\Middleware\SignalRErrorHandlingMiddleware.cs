using Microsoft.AspNetCore.SignalR;
using System.Net;

namespace FleetXQ.Api.Middleware;

/// <summary>
/// Middleware for handling SignalR errors and providing consistent error responses
/// </summary>
public class SignalRErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SignalRErrorHandlingMiddleware> _logger;

    public SignalRErrorHandlingMiddleware(RequestDelegate next, ILogger<SignalRErrorHandlingMiddleware> logger)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            // Only handle SignalR-related requests
            if (context.Request.Path.StartsWithSegments("/hubs"))
            {
                await HandleSignalRExceptionAsync(context, ex);
            }
            else
            {
                throw; // Re-throw for non-SignalR requests
            }
        }
    }

    private async Task HandleSignalRExceptionAsync(HttpContext context, Exception exception)
    {
        _logger.LogError(exception, "SignalR error occurred for path: {Path}", context.Request.Path);

        var response = context.Response;
        response.ContentType = "application/json";

        var errorResponse = exception switch
        {
            UnauthorizedAccessException => new
            {
                error = "Unauthorized",
                message = "Authentication required for SignalR connection",
                statusCode = (int)HttpStatusCode.Unauthorized
            },
            ArgumentException argEx => new
            {
                error = "BadRequest",
                message = argEx.Message,
                statusCode = (int)HttpStatusCode.BadRequest
            },
            InvalidOperationException => new
            {
                error = "InvalidOperation",
                message = "Invalid SignalR operation",
                statusCode = (int)HttpStatusCode.BadRequest
            },
            TimeoutException => new
            {
                error = "Timeout",
                message = "SignalR connection timed out",
                statusCode = (int)HttpStatusCode.RequestTimeout
            },
            HubException hubEx => new
            {
                error = "HubError",
                message = hubEx.Message,
                statusCode = (int)HttpStatusCode.BadRequest
            },
            _ => new
            {
                error = "InternalServerError",
                message = "An internal error occurred in SignalR",
                statusCode = (int)HttpStatusCode.InternalServerError
            }
        };

        response.StatusCode = errorResponse.statusCode;

        var jsonResponse = System.Text.Json.JsonSerializer.Serialize(errorResponse, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });

        await response.WriteAsync(jsonResponse);
    }
}

/// <summary>
/// Custom SignalR error filter for handling hub method exceptions
/// </summary>
public class SignalRErrorFilter : IHubFilter
{
    private readonly ILogger<SignalRErrorFilter> _logger;

    public SignalRErrorFilter(ILogger<SignalRErrorFilter> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async ValueTask<object?> InvokeMethodAsync(
        HubInvocationContext invocationContext, 
        Func<HubInvocationContext, ValueTask<object?>> next)
    {
        try
        {
            return await next(invocationContext);
        }
        catch (Exception ex)
        {
            var hubName = invocationContext.Hub.GetType().Name;
            var methodName = invocationContext.HubMethodName;
            var connectionId = invocationContext.Context.ConnectionId;
            var userId = invocationContext.Context.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            _logger.LogError(ex, "Error in {HubName}.{MethodName} for connection {ConnectionId} (User: {UserId})", 
                hubName, methodName, connectionId, userId);

            // Create a user-friendly error message
            var errorMessage = ex switch
            {
                UnauthorizedAccessException => "You don't have permission to perform this action",
                ArgumentException argEx => $"Invalid argument: {argEx.Message}",
                InvalidOperationException => "This operation is not valid at this time",
                TimeoutException => "The operation timed out. Please try again",
                _ => "An error occurred while processing your request"
            };

            // Send error to the caller
            await invocationContext.Context.Clients.Caller.SendAsync("Error", new
            {
                Type = ex.GetType().Name,
                Message = errorMessage,
                Method = methodName,
                Timestamp = DateTime.UtcNow
            });

            // Don't re-throw to prevent connection termination
            return null;
        }
    }

    public async Task OnConnectedAsync(HubLifetimeContext context, Func<HubLifetimeContext, Task> next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            var hubName = context.Hub.GetType().Name;
            var connectionId = context.Context.ConnectionId;
            var userId = context.Context.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            _logger.LogError(ex, "Error during connection to {HubName} for connection {ConnectionId} (User: {UserId})", 
                hubName, connectionId, userId);

            // Send connection error to client
            await context.Context.Clients.Caller.SendAsync("ConnectionError", new
            {
                Message = "Failed to establish connection",
                Timestamp = DateTime.UtcNow
            });

            throw; // Re-throw to prevent connection
        }
    }

    public async Task OnDisconnectedAsync(HubLifetimeContext context, Exception? exception, Func<HubLifetimeContext, Exception?, Task> next)
    {
        try
        {
            await next(context, exception);
        }
        catch (Exception ex)
        {
            var hubName = context.Hub.GetType().Name;
            var connectionId = context.Context.ConnectionId;
            var userId = context.Context.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            _logger.LogError(ex, "Error during disconnection from {HubName} for connection {ConnectionId} (User: {UserId})", 
                hubName, connectionId, userId);

            // Don't re-throw during disconnection
        }
    }
}
