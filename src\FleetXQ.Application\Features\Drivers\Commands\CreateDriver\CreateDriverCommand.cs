using MediatR;

namespace FleetXQ.Application.Features.Drivers.Commands.CreateDriver;

/// <summary>
/// Command to create a new driver
/// </summary>
public sealed class CreateDriverCommand : IRequest<CreateDriverResult>
{
    /// <summary>
    /// Gets or sets the first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email address
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the license number
    /// </summary>
    public string? LicenseNumber { get; set; }

    /// <summary>
    /// Gets or sets the license class
    /// </summary>
    public string? LicenseClass { get; set; }

    /// <summary>
    /// Gets or sets the employee ID
    /// </summary>
    public string? EmployeeId { get; set; }

    /// <summary>
    /// Gets or sets the date of birth
    /// </summary>
    public DateTime? DateOfBirth { get; set; }

    /// <summary>
    /// Gets or sets the hire date
    /// </summary>
    public DateTime HireDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Result of creating a driver
/// </summary>
public sealed class CreateDriverResult
{
    /// <summary>
    /// Gets or sets the created driver
    /// </summary>
    public FleetXQ.Application.Features.Drivers.DTOs.DriverDto? Driver { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="driver">The created driver</param>
    /// <returns>A successful result</returns>
    public static CreateDriverResult Successful(FleetXQ.Application.Features.Drivers.DTOs.DriverDto driver)
    {
        return new CreateDriverResult
        {
            Driver = driver,
            Success = true
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static CreateDriverResult Failed(string errorMessage)
    {
        return new CreateDriverResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
