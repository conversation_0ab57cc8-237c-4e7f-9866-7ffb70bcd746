using FleetXQ.Api.Models;
using FleetXQ.Api.Tests.Builders;
using FleetXQ.Api.Tests.Integration;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;
using NBomber.Contracts;
using NBomber.CSharp;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace FleetXQ.Api.Tests.Performance;

/// <summary>
/// Performance tests for API endpoints
/// </summary>
public class ApiPerformanceTests : IClassFixture<IntegrationTestWebApplicationFactory>
{
    private readonly IntegrationTestWebApplicationFactory _factory;
    private readonly JsonSerializerOptions _jsonOptions;

    public ApiPerformanceTests(IntegrationTestWebApplicationFactory factory)
    {
        _factory = factory;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task GetVehicles_LoadTest_ShouldHandleConcurrentRequests()
    {
        // Arrange
        await SeedTestDataAsync();
        var token = CreateTestToken();

        var scenario = Scenario.Create("get_vehicles_load_test", async context =>
        {
            using var httpClient = _factory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var query = new GetVehicleListQuery { PageNumber = 1, PageSize = 10 };
            var json = JsonSerializer.Serialize(query, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/vehicles/list", content);

            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromSeconds(30))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert
        var sceneStats = stats.AllScenarios.First();
        sceneStats.Ok.Request.Count.Should().BeGreaterThan(250); // At least 250 successful requests
        sceneStats.Ok.Request.RPS.Should().BeGreaterThan(8); // At least 8 RPS
        sceneStats.Fail.Request.Count.Should().BeLessThan(10); // Less than 10 failed requests
        sceneStats.Ok.Latency.Mean.Should().BeLessThan(500); // Mean latency under 500ms
    }

    [Fact]
    public async Task Authentication_LoadTest_ShouldHandleLoginRequests()
    {
        // Arrange
        await SeedTestUsersAsync();

        var scenario = Scenario.Create("authentication_load_test", async context =>
        {
            using var httpClient = _factory.CreateClient();

            var loginRequest = new
            {
                Username = "testuser",
                Password = "TestPassword123!"
            };

            var json = JsonSerializer.Serialize(loginRequest, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/auth/login", content);

            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 5, during: TimeSpan.FromSeconds(20))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert
        var sceneStats = stats.AllScenarios.First();
        sceneStats.Ok.Request.Count.Should().BeGreaterThan(80); // At least 80 successful requests
        sceneStats.Fail.Request.Count.Should().BeLessThan(5); // Less than 5 failed requests
        sceneStats.Ok.Latency.Mean.Should().BeLessThan(1000); // Mean latency under 1 second
    }

    [Fact]
    public async Task MixedWorkload_LoadTest_ShouldHandleRealisticTraffic()
    {
        // Arrange
        await SeedTestDataAsync();
        var token = CreateTestToken();

        var getVehiclesScenario = Scenario.Create("get_vehicles", async context =>
        {
            using var httpClient = _factory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var query = new GetVehicleListQuery { PageNumber = 1, PageSize = 10 };
            var json = JsonSerializer.Serialize(query, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/vehicles/list", content);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithWeight(60) // 60% of traffic
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 6, during: TimeSpan.FromSeconds(30))
        );

        var getDriversScenario = Scenario.Create("get_drivers", async context =>
        {
            using var httpClient = _factory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await httpClient.GetAsync("/api/drivers");
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithWeight(30) // 30% of traffic
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 3, during: TimeSpan.FromSeconds(30))
        );

        var authScenario = Scenario.Create("authentication", async context =>
        {
            using var httpClient = _factory.CreateClient();

            var loginRequest = new
            {
                Username = "testuser",
                Password = "TestPassword123!"
            };

            var json = JsonSerializer.Serialize(loginRequest, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/auth/login", content);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithWeight(10) // 10% of traffic
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 1, during: TimeSpan.FromSeconds(30))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(getVehiclesScenario, getDriversScenario, authScenario)
            .Run();

        // Assert
        foreach (var scenario in stats.AllScenarios)
        {
            scenario.Ok.Request.Count.Should().BeGreaterThan(0);
            scenario.Fail.Request.Count.Should().BeLessThan(scenario.Ok.Request.Count * 0.05); // Less than 5% failure rate
            scenario.Ok.Latency.Mean.Should().BeLessThan(1000); // Mean latency under 1 second
        }
    }

    [Fact]
    public async Task DatabaseStress_LoadTest_ShouldHandleHighDatabaseLoad()
    {
        // Arrange
        await SeedLargeDatasetAsync();
        var token = CreateTestToken();

        var scenario = Scenario.Create("database_stress_test", async context =>
        {
            using var httpClient = _factory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Simulate complex queries with different page sizes
            var pageSize = context.InvocationNumber % 3 switch
            {
                0 => 10,
                1 => 25,
                _ => 50
            };

            var query = new GetVehicleListQuery 
            { 
                PageNumber = (context.InvocationNumber % 10) + 1, 
                PageSize = pageSize 
            };

            var json = JsonSerializer.Serialize(query, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/vehicles/list", content);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 15, during: TimeSpan.FromSeconds(20))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert
        var sceneStats = stats.AllScenarios.First();
        sceneStats.Ok.Request.Count.Should().BeGreaterThan(250);
        sceneStats.Fail.Request.Count.Should().BeLessThan(15); // Less than 15 failed requests
        sceneStats.Ok.Latency.Mean.Should().BeLessThan(800); // Mean latency under 800ms
        sceneStats.Ok.Latency.P95.Should().BeLessThan(1500); // 95th percentile under 1.5 seconds
    }

    private async Task SeedTestDataAsync()
    {
        await _factory.SeedDatabaseAsync(async context =>
        {
            var user = TestDataBuilder.CreateUser(username: "testuser", role: UserRole.User);
            var vehicles = TestDataBuilder.CreateVehicles(50);
            var drivers = TestDataBuilder.CreateDrivers(25);

            context.Users.Add(user);
            context.Vehicles.AddRange(vehicles);
            context.Drivers.AddRange(drivers);
        });
    }

    private async Task SeedTestUsersAsync()
    {
        await _factory.SeedDatabaseAsync(async context =>
        {
            var passwordHashingService = _factory.GetRequiredService<FleetXQ.Application.Interfaces.IPasswordHashingService>();
            var passwordHash = passwordHashingService.HashPassword("TestPassword123!");
            
            var user = TestDataBuilder.CreateUser(username: "testuser", role: UserRole.User);
            
            // Update password hash using reflection
            var passwordHashProperty = typeof(FleetXQ.Domain.Entities.User).GetProperty("PasswordHash");
            passwordHashProperty?.SetValue(user, passwordHash);

            context.Users.Add(user);
        });
    }

    private async Task SeedLargeDatasetAsync()
    {
        await _factory.SeedDatabaseAsync(async context =>
        {
            var user = TestDataBuilder.CreateUser(username: "testuser", role: UserRole.User);
            var vehicles = TestDataBuilder.CreateVehicles(500); // Large dataset
            var drivers = TestDataBuilder.CreateDrivers(200);

            context.Users.Add(user);
            context.Vehicles.AddRange(vehicles);
            context.Drivers.AddRange(drivers);
        });
    }

    private string CreateTestToken()
    {
        var userId = Guid.NewGuid();
        return CreateJwtToken(userId, "testuser", "<EMAIL>", new[] { "User" });
    }

    private string CreateJwtToken(Guid userId, string username, string email, string[] roles)
    {
        // This is a simplified version - in real implementation, use the same logic as ApiIntegrationTestBase
        var key = new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(
            Encoding.UTF8.GetBytes("YourSuperSecretKeyThatIsAtLeast32CharactersLong!"));
        var credentials = new Microsoft.IdentityModel.Tokens.SigningCredentials(
            key, Microsoft.IdentityModel.Tokens.SecurityAlgorithms.HmacSha256);

        var claims = new List<System.Security.Claims.Claim>
        {
            new(System.Security.Claims.ClaimTypes.NameIdentifier, userId.ToString()),
            new(System.Security.Claims.ClaimTypes.Name, username),
            new(System.Security.Claims.ClaimTypes.Email, email),
            new(System.IdentityModel.Tokens.Jwt.JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new("IsActive", "True")
        };

        claims.AddRange(roles.Select(role => new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Role, role)));

        var token = new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(
            issuer: "FleetXQ",
            audience: "FleetXQ-Users",
            claims: claims,
            expires: DateTime.UtcNow.AddHours(1),
            signingCredentials: credentials);

        return new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler().WriteToken(token);
    }
}
