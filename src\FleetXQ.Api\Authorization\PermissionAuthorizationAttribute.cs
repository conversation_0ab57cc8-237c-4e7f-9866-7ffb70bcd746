using FleetXQ.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace FleetXQ.Api.Authorization;

/// <summary>
/// Authorization attribute that checks for specific permissions
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public sealed class PermissionAuthorizationAttribute : Attribute, IAuthorizationFilter
{
    private readonly string _permission;

    /// <summary>
    /// Initializes a new instance of the <see cref="PermissionAuthorizationAttribute"/> class
    /// </summary>
    /// <param name="permission">The required permission</param>
    public PermissionAuthorizationAttribute(string permission)
    {
        _permission = permission ?? throw new ArgumentNullException(nameof(permission));
    }

    /// <summary>
    /// Called when authorization is required
    /// </summary>
    /// <param name="context">The authorization filter context</param>
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        // Skip authorization if the action has AllowAnonymous attribute
        if (context.ActionDescriptor.EndpointMetadata.Any(em => em.GetType() == typeof(AllowAnonymousAttribute)))
        {
            return;
        }

        // Check if user is authenticated
        if (context.HttpContext.User?.Identity?.IsAuthenticated != true)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // Get user context from middleware
        if (!context.HttpContext.Items.TryGetValue("UserContext", out var userContextObj) ||
            userContextObj is not UserContext userContext)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // Check if user is active
        if (!userContext.IsActive)
        {
            context.Result = new ForbidResult();
            return;
        }

        // Check if user has the required permission
        if (!userContext.Permissions.Contains(_permission))
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<PermissionAuthorizationAttribute>>();
            logger.LogWarning("Access denied for user {UserId} - missing permission {Permission}", 
                userContext.UserId, _permission);

            context.Result = new ForbidResult();
            return;
        }
    }
}

/// <summary>
/// Authorization attribute for resource-specific access control
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public sealed class ResourceAuthorizationAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly string _resourceType;
    private readonly string _action;
    private readonly string? _resourceIdParameter;

    /// <summary>
    /// Initializes a new instance of the <see cref="ResourceAuthorizationAttribute"/> class
    /// </summary>
    /// <param name="resourceType">The resource type</param>
    /// <param name="action">The action being performed</param>
    /// <param name="resourceIdParameter">The parameter name containing the resource ID (optional)</param>
    public ResourceAuthorizationAttribute(string resourceType, string action, string? resourceIdParameter = null)
    {
        _resourceType = resourceType ?? throw new ArgumentNullException(nameof(resourceType));
        _action = action ?? throw new ArgumentNullException(nameof(action));
        _resourceIdParameter = resourceIdParameter;
    }

    /// <summary>
    /// Called when authorization is required
    /// </summary>
    /// <param name="context">The authorization filter context</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        // Skip authorization if the action has AllowAnonymous attribute
        if (context.ActionDescriptor.EndpointMetadata.Any(em => em.GetType() == typeof(AllowAnonymousAttribute)))
        {
            return;
        }

        // Check if user is authenticated
        if (context.HttpContext.User?.Identity?.IsAuthenticated != true)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // Get user context from middleware
        if (!context.HttpContext.Items.TryGetValue("UserContext", out var userContextObj) ||
            userContextObj is not UserContext userContext)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // Check if user is active
        if (!userContext.IsActive)
        {
            context.Result = new ForbidResult();
            return;
        }

        // Get resource ID if specified
        Guid? resourceId = null;
        if (!string.IsNullOrWhiteSpace(_resourceIdParameter))
        {
            var routeValues = context.HttpContext.Request.RouteValues;
            if (routeValues.TryGetValue(_resourceIdParameter, out var resourceIdValue) &&
                Guid.TryParse(resourceIdValue?.ToString(), out var parsedResourceId))
            {
                resourceId = parsedResourceId;
            }
        }

        // Get authorization service and check access
        var authorizationService = context.HttpContext.RequestServices.GetRequiredService<IAuthorizationService>();
        var canAccess = await authorizationService.CanAccessResourceAsync(
            userContext.UserId, _resourceType, resourceId, _action);

        if (!canAccess)
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<ResourceAuthorizationAttribute>>();
            logger.LogWarning("Resource access denied for user {UserId} - {ResourceType}:{ResourceId} action {Action}", 
                userContext.UserId, _resourceType, resourceId, _action);

            context.Result = new ForbidResult();
            return;
        }
    }
}

/// <summary>
/// Common permission constants
/// </summary>
public static class Permissions
{
    // Vehicle permissions
    public const string VehiclesRead = "vehicles.read";
    public const string VehiclesWrite = "vehicles.write";
    public const string VehiclesDelete = "vehicles.delete";

    // Driver permissions
    public const string DriversRead = "drivers.read";
    public const string DriversWrite = "drivers.write";
    public const string DriversDelete = "drivers.delete";

    // User permissions
    public const string UsersRead = "users.read";
    public const string UsersWrite = "users.write";
    public const string UsersDelete = "users.delete";

    // Alert permissions
    public const string AlertsRead = "alerts.read";
    public const string AlertsWrite = "alerts.write";
    public const string AlertsDelete = "alerts.delete";

    // Report permissions
    public const string ReportsRead = "reports.read";
    public const string ReportsWrite = "reports.write";

    // Telemetry permissions
    public const string TelemetryRead = "telemetry.read";
    public const string TelemetryWrite = "telemetry.write";

    // Profile permissions
    public const string ProfileRead = "profile.read";
    public const string ProfileWrite = "profile.write";

    // System permissions
    public const string SystemAdmin = "system.admin";
}

/// <summary>
/// Resource type constants
/// </summary>
public static class ResourceTypes
{
    public const string Vehicles = "vehicles";
    public const string Drivers = "drivers";
    public const string Users = "users";
    public const string Alerts = "alerts";
    public const string Reports = "reports";
    public const string Telemetry = "telemetry";
}

/// <summary>
/// Action constants
/// </summary>
public static class Actions
{
    public const string Read = "read";
    public const string Write = "write";
    public const string Delete = "delete";
    public const string Create = "create";
    public const string Update = "update";
}
