using MediatR;

namespace FleetXQ.Application.Features.Drivers.Queries.GetDriverById;

/// <summary>
/// Query to get a driver by ID
/// </summary>
public sealed class GetDriverByIdQuery : IRequest<GetDriverByIdResult>
{
    /// <summary>
    /// Gets or sets the driver ID
    /// </summary>
    public Guid DriverId { get; set; }
}

/// <summary>
/// Result of getting a driver by ID
/// </summary>
public sealed class GetDriverByIdResult
{
    /// <summary>
    /// Gets or sets the driver
    /// </summary>
    public FleetXQ.Application.Features.Drivers.DTOs.DriverDto? Driver { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="driver">The driver</param>
    /// <returns>A successful result</returns>
    public static GetDriverByIdResult Successful(FleetXQ.Application.Features.Drivers.DTOs.DriverDto driver)
    {
        return new GetDriverByIdResult
        {
            Driver = driver,
            Success = true
        };
    }

    /// <summary>
    /// Creates a not found result
    /// </summary>
    /// <returns>A not found result</returns>
    public static GetDriverByIdResult NotFound()
    {
        return new GetDriverByIdResult
        {
            Success = false,
            ErrorMessage = "Driver not found"
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static GetDriverByIdResult Failed(string errorMessage)
    {
        return new GetDriverByIdResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
