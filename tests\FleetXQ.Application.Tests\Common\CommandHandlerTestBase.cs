using FleetXQ.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Moq;

namespace FleetXQ.Application.Tests.Common;

/// <summary>
/// Base class for command handler tests
/// </summary>
/// <typeparam name="THandler">The command handler type</typeparam>
public abstract class CommandHandlerTestBase<THandler> : TestBase
    where THandler : class
{
    protected readonly Mock<ILogger<THandler>> MockLogger;

    protected CommandHandlerTestBase()
    {
        MockLogger = CreateMockLogger<THandler>();
    }

    /// <summary>
    /// Verifies that the handler logged an information message
    /// </summary>
    /// <param name="times">The expected number of times (default: once)</param>
    protected void VerifyInformationLogged(Times? times = null)
    {
        VerifyLoggerCalled(MockLogger, LogLevel.Information, times);
    }

    /// <summary>
    /// Verifies that the handler logged a warning message
    /// </summary>
    /// <param name="times">The expected number of times (default: once)</param>
    protected void VerifyWarningLogged(Times? times = null)
    {
        VerifyLoggerCalled(MockLogger, LogLevel.Warning, times);
    }

    /// <summary>
    /// Verifies that the handler logged an error message
    /// </summary>
    /// <param name="times">The expected number of times (default: once)</param>
    protected void VerifyErrorLogged(Times? times = null)
    {
        VerifyLoggerCalled(MockLogger, LogLevel.Error, times);
    }

    /// <summary>
    /// Verifies that repository update was called
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <param name="mockRepository">The mock repository</param>
    /// <param name="times">The expected number of times (default: once)</param>
    protected static void VerifyRepositoryUpdateCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository, Times? times = null)
        where TEntity : class
    {
        mockRepository.Verify(
            x => x.UpdateAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that repository add was called
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <param name="mockRepository">The mock repository</param>
    /// <param name="times">The expected number of times (default: once)</param>
    protected static void VerifyRepositoryAddCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository, Times? times = null)
        where TEntity : class
    {
        mockRepository.Verify(
            x => x.AddAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that repository delete was called
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <param name="mockRepository">The mock repository</param>
    /// <param name="times">The expected number of times (default: once)</param>
    protected static void VerifyRepositoryDeleteCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository, Times? times = null)
        where TEntity : class
    {
        mockRepository.Verify(
            x => x.DeleteAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()),
            times ?? Times.Once);
    }

    /// <summary>
    /// Sets up a repository to throw an exception when called
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <param name="mockRepository">The mock repository</param>
    /// <param name="exception">The exception to throw</param>
    protected static void SetupRepositoryToThrow<TEntity>(Mock<IRepository<TEntity>> mockRepository, Exception exception)
        where TEntity : class
    {
        mockRepository.Setup(x => x.AddAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);
        mockRepository.Setup(x => x.UpdateAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);
        mockRepository.Setup(x => x.DeleteAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);
    }

    /// <summary>
    /// Verifies that no repository operations were called
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <param name="mockRepository">The mock repository</param>
    protected static void VerifyNoRepositoryOperationsCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository)
        where TEntity : class
    {
        mockRepository.Verify(x => x.AddAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()), Times.Never);
        mockRepository.Verify(x => x.UpdateAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()), Times.Never);
        mockRepository.Verify(x => x.DeleteAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()), Times.Never);
    }
}
