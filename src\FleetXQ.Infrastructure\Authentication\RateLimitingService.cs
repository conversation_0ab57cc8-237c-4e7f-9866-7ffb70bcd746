using FleetXQ.Application.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

/// <summary>
/// Rate limiting configuration for different endpoints
/// </summary>
public sealed class RateLimitConfig
{
    /// <summary>
    /// Gets or sets the maximum number of requests allowed in the time window
    /// </summary>
    public int MaxRequests { get; set; } = 5;

    /// <summary>
    /// Gets or sets the time window for rate limiting
    /// </summary>
    public TimeSpan TimeWindow { get; set; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Gets or sets the lockout duration after exceeding the limit
    /// </summary>
    public TimeSpan LockoutDuration { get; set; } = TimeSpan.FromMinutes(30);
}

/// <summary>
/// Rate limiting attempt record
/// </summary>
internal sealed class RateLimitRecord
{
    public int AttemptCount { get; set; }
    public DateTime FirstAttempt { get; set; }
    public DateTime? LockoutUntil { get; set; }
    public List<DateTime> Attempts { get; set; } = new();
}

/// <summary>
/// Implementation of rate limiting service using in-memory cache
/// </summary>
public sealed class RateLimitingService : IRateLimitingService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<RateLimitingService> _logger;
    private readonly Dictionary<string, RateLimitConfig> _endpointConfigs;

    /// <summary>
    /// Initializes a new instance of the <see cref="RateLimitingService"/> class
    /// </summary>
    /// <param name="cache">The memory cache</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="logger">The logger</param>
    public RateLimitingService(IMemoryCache cache, IConfiguration configuration, ILogger<RateLimitingService> logger)
    {
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load rate limiting configurations for different endpoints
        _endpointConfigs = new Dictionary<string, RateLimitConfig>
        {
            ["login"] = new RateLimitConfig
            {
                MaxRequests = configuration.GetValue<int>("RateLimit:Login:MaxRequests", 5),
                TimeWindow = TimeSpan.FromMinutes(configuration.GetValue<int>("RateLimit:Login:TimeWindowMinutes", 15)),
                LockoutDuration = TimeSpan.FromMinutes(configuration.GetValue<int>("RateLimit:Login:LockoutDurationMinutes", 30))
            },
            ["refresh"] = new RateLimitConfig
            {
                MaxRequests = configuration.GetValue<int>("RateLimit:Refresh:MaxRequests", 10),
                TimeWindow = TimeSpan.FromMinutes(configuration.GetValue<int>("RateLimit:Refresh:TimeWindowMinutes", 5)),
                LockoutDuration = TimeSpan.FromMinutes(configuration.GetValue<int>("RateLimit:Refresh:LockoutDurationMinutes", 15))
            },
            ["password-reset"] = new RateLimitConfig
            {
                MaxRequests = configuration.GetValue<int>("RateLimit:PasswordReset:MaxRequests", 3),
                TimeWindow = TimeSpan.FromHours(configuration.GetValue<int>("RateLimit:PasswordReset:TimeWindowHours", 1)),
                LockoutDuration = TimeSpan.FromHours(configuration.GetValue<int>("RateLimit:PasswordReset:LockoutDurationHours", 2))
            }
        };

        _logger.LogInformation("Rate limiting service initialized with {EndpointCount} endpoint configurations", _endpointConfigs.Count);
    }

    /// <summary>
    /// Checks if a request is allowed based on rate limiting rules
    /// </summary>
    /// <param name="key">The rate limiting key (e.g., IP address, user ID)</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>True if the request is allowed</returns>
    public async Task<bool> IsRequestAllowedAsync(string key, string endpoint)
    {
        if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(endpoint))
            return true;

        var config = GetEndpointConfig(endpoint);
        var cacheKey = GetCacheKey(key, endpoint);
        var record = _cache.Get<RateLimitRecord>(cacheKey);

        if (record == null)
        {
            _logger.LogDebug("No rate limit record found for {Key}:{Endpoint}, allowing request", key, endpoint);
            return true;
        }

        var now = DateTime.UtcNow;

        // Check if currently locked out
        if (record.LockoutUntil.HasValue && record.LockoutUntil.Value > now)
        {
            _logger.LogWarning("Rate limit lockout active for {Key}:{Endpoint} until {LockoutUntil}", 
                key, endpoint, record.LockoutUntil.Value);
            return false;
        }

        // Clean up old attempts outside the time window
        record.Attempts.RemoveAll(attempt => now - attempt > config.TimeWindow);

        // Check if within rate limit
        var isAllowed = record.Attempts.Count < config.MaxRequests;
        
        _logger.LogDebug("Rate limit check for {Key}:{Endpoint}: {AttemptCount}/{MaxRequests} attempts, allowed: {IsAllowed}",
            key, endpoint, record.Attempts.Count, config.MaxRequests, isAllowed);

        return isAllowed;
    }

    /// <summary>
    /// Records a failed authentication attempt
    /// </summary>
    /// <param name="key">The rate limiting key</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task RecordFailedAttemptAsync(string key, string endpoint)
    {
        if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(endpoint))
            return;

        var config = GetEndpointConfig(endpoint);
        var cacheKey = GetCacheKey(key, endpoint);
        var now = DateTime.UtcNow;

        var record = _cache.Get<RateLimitRecord>(cacheKey) ?? new RateLimitRecord
        {
            FirstAttempt = now
        };

        // Clean up old attempts
        record.Attempts.RemoveAll(attempt => now - attempt > config.TimeWindow);

        // Add current attempt
        record.Attempts.Add(now);
        record.AttemptCount++;

        // Check if lockout should be applied
        if (record.Attempts.Count >= config.MaxRequests)
        {
            record.LockoutUntil = now.Add(config.LockoutDuration);
            _logger.LogWarning("Rate limit exceeded for {Key}:{Endpoint}, applying lockout until {LockoutUntil}",
                key, endpoint, record.LockoutUntil.Value);
        }

        // Cache the record with appropriate expiration
        var cacheExpiration = record.LockoutUntil ?? now.Add(config.TimeWindow);
        _cache.Set(cacheKey, record, cacheExpiration);

        _logger.LogDebug("Recorded failed attempt for {Key}:{Endpoint}, total attempts: {AttemptCount}",
            key, endpoint, record.Attempts.Count);
    }

    /// <summary>
    /// Records a successful authentication attempt
    /// </summary>
    /// <param name="key">The rate limiting key</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task RecordSuccessfulAttemptAsync(string key, string endpoint)
    {
        if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(endpoint))
            return;

        var cacheKey = GetCacheKey(key, endpoint);
        
        // Clear the rate limit record on successful authentication
        _cache.Remove(cacheKey);

        _logger.LogDebug("Cleared rate limit record for {Key}:{Endpoint} after successful attempt", key, endpoint);
    }

    /// <summary>
    /// Gets the remaining time until rate limit reset
    /// </summary>
    /// <param name="key">The rate limiting key</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>The remaining time until reset</returns>
    public async Task<TimeSpan?> GetRemainingLockoutTimeAsync(string key, string endpoint)
    {
        if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(endpoint))
            return null;

        var cacheKey = GetCacheKey(key, endpoint);
        var record = _cache.Get<RateLimitRecord>(cacheKey);

        if (record?.LockoutUntil == null)
            return null;

        var now = DateTime.UtcNow;
        if (record.LockoutUntil.Value <= now)
            return null;

        return record.LockoutUntil.Value - now;
    }

    /// <summary>
    /// Gets the rate limit configuration for an endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint</param>
    /// <returns>The rate limit configuration</returns>
    private RateLimitConfig GetEndpointConfig(string endpoint)
    {
        return _endpointConfigs.TryGetValue(endpoint.ToLowerInvariant(), out var config) 
            ? config 
            : _endpointConfigs["login"]; // Default to login config
    }

    /// <summary>
    /// Gets the cache key for rate limiting
    /// </summary>
    /// <param name="key">The rate limiting key</param>
    /// <param name="endpoint">The endpoint</param>
    /// <returns>The cache key</returns>
    private static string GetCacheKey(string key, string endpoint)
    {
        return $"rate_limit:{endpoint}:{key}";
    }
}
