using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

/// <summary>
/// Implementation of authorization service
/// </summary>
public sealed class AuthorizationService : IAuthorizationService
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<AuthorizationService> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="AuthorizationService"/> class
    /// </summary>
    /// <param name="userRepository">The user repository</param>
    /// <param name="logger">The logger</param>
    public AuthorizationService(IUserRepository userRepository, ILogger<AuthorizationService> logger)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Checks if a user has a specific role
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="requiredRole">The required role</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the user has the required role</returns>
    public async Task<bool> HasRoleAsync(Guid userId, UserRole requiredRole, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null || !user.IsActive || user.IsLockedOut)
            {
                _logger.LogDebug("Role check failed for user {UserId} - user not found, inactive, or locked out", userId);
                return false;
            }

            var hasRole = user.HasRoleOrHigher(requiredRole);
            _logger.LogDebug("Role check for user {UserId} requiring {RequiredRole}: {HasRole}", userId, requiredRole, hasRole);

            return hasRole;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Checks if a user has a specific permission
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="permission">The required permission</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the user has the required permission</returns>
    public async Task<bool> HasPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await GetUserPermissionsAsync(userId, cancellationToken);
            var hasPermission = permissions.Contains(permission);

            _logger.LogDebug("Permission check for user {UserId} requiring {Permission}: {HasPermission}", userId, permission, hasPermission);

            return hasPermission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Checks if a user can access a specific resource
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="resourceType">The resource type</param>
    /// <param name="resourceId">The resource ID</param>
    /// <param name="action">The action to perform</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the user can access the resource</returns>
    public async Task<bool> CanAccessResourceAsync(Guid userId, string resourceType, Guid? resourceId, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null || !user.IsActive || user.IsLockedOut)
            {
                _logger.LogDebug("Resource access denied for user {UserId} - user not found, inactive, or locked out", userId);
                return false;
            }

            // Check basic permission for the resource type and action
            var permission = $"{resourceType}.{action}";
            var hasPermission = await HasPermissionAsync(userId, permission, cancellationToken);

            if (!hasPermission)
            {
                _logger.LogDebug("Resource access denied for user {UserId} - missing permission {Permission}", userId, permission);
                return false;
            }

            // Additional resource-specific checks can be added here
            // For example, drivers can only access their assigned vehicles
            if (user.Role == UserRole.Driver && resourceType == "vehicles" && resourceId.HasValue)
            {
                // TODO: Implement driver-vehicle assignment check
                // This would require checking if the driver is assigned to the specific vehicle
                _logger.LogDebug("Driver resource access check for user {UserId} and vehicle {VehicleId}", userId, resourceId);
            }

            _logger.LogDebug("Resource access granted for user {UserId} to {ResourceType}:{ResourceId} for action {Action}", 
                userId, resourceType, resourceId, action);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking resource access for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Gets all permissions for a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The list of permissions</returns>
    public async Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null || !user.IsActive || user.IsLockedOut)
            {
                _logger.LogDebug("No permissions for user {UserId} - user not found, inactive, or locked out", userId);
                return new List<string>();
            }

            var permissions = GetRolePermissions(user.Role);
            _logger.LogDebug("Retrieved {PermissionCount} permissions for user {UserId} with role {Role}", 
                permissions.Count, userId, user.Role);

            return permissions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for user {UserId}", userId);
            return new List<string>();
        }
    }

    /// <summary>
    /// Validates user context from claims
    /// </summary>
    /// <param name="claims">The user claims</param>
    /// <returns>The user context information</returns>
    public UserContext? ValidateUserContext(ClaimsPrincipal claims)
    {
        try
        {
            var userIdClaim = claims.FindFirst(ClaimTypes.NameIdentifier);
            var usernameClaim = claims.FindFirst(ClaimTypes.Name);
            var roleClaim = claims.FindFirst(ClaimTypes.Role);
            var isActiveClaim = claims.FindFirst("is_active");

            if (userIdClaim == null || usernameClaim == null || roleClaim == null)
            {
                _logger.LogDebug("Invalid user context - missing required claims");
                return null;
            }

            if (!Guid.TryParse(userIdClaim.Value, out var userId))
            {
                _logger.LogDebug("Invalid user context - invalid user ID format");
                return null;
            }

            if (!Enum.TryParse<UserRole>(roleClaim.Value, out var role))
            {
                _logger.LogDebug("Invalid user context - invalid role format");
                return null;
            }

            var isActive = isActiveClaim?.Value == "True";

            var userContext = new UserContext
            {
                UserId = userId,
                Username = usernameClaim.Value,
                Role = role,
                IsActive = isActive,
                Permissions = GetRolePermissions(role)
            };

            _logger.LogDebug("User context validated for user {UserId} with role {Role}", userId, role);

            return userContext;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating user context");
            return null;
        }
    }

    /// <summary>
    /// Gets the permissions for a specific role
    /// </summary>
    /// <param name="role">The user role</param>
    /// <returns>The list of permissions</returns>
    private static List<string> GetRolePermissions(UserRole role)
    {
        return role switch
        {
            UserRole.Admin => new List<string>
            {
                "vehicles.read", "vehicles.write", "vehicles.delete",
                "drivers.read", "drivers.write", "drivers.delete",
                "users.read", "users.write", "users.delete",
                "alerts.read", "alerts.write", "alerts.delete",
                "reports.read", "reports.write",
                "telemetry.read", "telemetry.write",
                "system.admin"
            },
            UserRole.Manager => new List<string>
            {
                "vehicles.read", "vehicles.write",
                "drivers.read", "drivers.write",
                "users.read",
                "alerts.read", "alerts.write",
                "reports.read", "reports.write",
                "telemetry.read"
            },
            UserRole.Driver => new List<string>
            {
                "vehicles.read",
                "telemetry.read",
                "profile.read", "profile.write"
            },
            UserRole.User => new List<string>
            {
                "vehicles.read",
                "alerts.read",
                "reports.read",
                "telemetry.read",
                "profile.read", "profile.write"
            },
            _ => new List<string>()
        };
    }
}
