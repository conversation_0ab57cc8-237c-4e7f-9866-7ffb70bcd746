{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "FleetXQ", "Audience": "FleetXQ-Users", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "PasswordPolicy": {"MinLength": 8, "MaxLength": 128, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialChar": true, "MinUniqueChars": 4}, "RateLimit": {"Login": {"MaxRequests": 5, "TimeWindowMinutes": 15, "LockoutDurationMinutes": 30}, "Refresh": {"MaxRequests": 10, "TimeWindowMinutes": 5, "LockoutDurationMinutes": 15}, "PasswordReset": {"MaxRequests": 3, "TimeWindowHours": 1, "LockoutDurationHours": 2}}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:5173", "https://localhost:5173"]}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/fleetxq-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}