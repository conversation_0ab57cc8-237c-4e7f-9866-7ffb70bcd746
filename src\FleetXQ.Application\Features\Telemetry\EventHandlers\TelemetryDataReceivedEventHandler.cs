using AutoMapper;
using FleetXQ.Application.Features.Telemetry.DTOs;
using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Events;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Telemetry.EventHandlers;

/// <summary>
/// Handler for TelemetryDataReceivedEvent that sends real-time updates via SignalR
/// </summary>
public class TelemetryDataReceivedEventHandler : INotificationHandler<TelemetryDataReceivedEvent>
{
    private readonly ISignalRNotificationService _signalRNotificationService;
    private readonly IMapper _mapper;
    private readonly ILogger<TelemetryDataReceivedEventHandler> _logger;

    public TelemetryDataReceivedEventHandler(
        ISignalRNotificationService signalRNotificationService,
        IMapper mapper,
        ILogger<TelemetryDataReceivedEventHandler> logger)
    {
        _signalRNotificationService = signalRNotificationService ?? throw new ArgumentNullException(nameof(signalRNotificationService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the TelemetryDataReceivedEvent by sending real-time updates to SignalR clients
    /// </summary>
    /// <param name="notification">The domain event</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task Handle(TelemetryDataReceivedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing TelemetryDataReceivedEvent for vehicle {VehicleId}", notification.VehicleId);

            // Create telemetry DTO from domain event
            var telemetryDto = CreateTelemetryDto(notification);

            // Send telemetry update to SignalR clients
            await _signalRNotificationService.SendTelemetryUpdateAsync(
                notification.VehicleId, 
                telemetryDto, 
                cancellationToken);

            // Send vehicle status update to dashboard
            var status = DetermineVehicleStatus(notification);
            await _signalRNotificationService.SendVehicleStatusUpdateAsync(
                notification.VehicleId,
                status,
                notification.Timestamp,
                cancellationToken);

            _logger.LogInformation("Successfully processed TelemetryDataReceivedEvent for vehicle {VehicleId}", 
                notification.VehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing TelemetryDataReceivedEvent for vehicle {VehicleId}", 
                notification.VehicleId);
            
            // Don't rethrow - we don't want to break the domain event processing pipeline
            // The telemetry data has already been saved, this is just for real-time notifications
        }
    }

    /// <summary>
    /// Creates a TelemetryDataDto from the domain event
    /// </summary>
    private static TelemetryDataDto CreateTelemetryDto(TelemetryDataReceivedEvent notification)
    {
        return new TelemetryDataDto
        {
            VehicleId = notification.VehicleId,
            Location = new FleetXQ.Application.Features.Vehicles.DTOs.LocationDto
            {
                Latitude = notification.Location.Latitude,
                Longitude = notification.Location.Longitude
            },
            SpeedKmh = notification.Speed.KilometersPerHour,
            SpeedMph = notification.Speed.MilesPerHour,
            FuelLevelPercentage = notification.FuelLevel?.Percentage,
            CurrentMileage = notification.Mileage ?? 0,
            IsMoving = notification.Speed.KilometersPerHour > 0,
            Timestamp = notification.Timestamp,
            EngineTemperature = null, // Not available in current domain event
            RPM = null, // Not available in current domain event
            AdditionalData = new Dictionary<string, object>
            {
                ["eventId"] = notification.Id,
                ["eventTimestamp"] = notification.OccurredOn
            }
        };
    }

    /// <summary>
    /// Determines vehicle status based on telemetry data
    /// </summary>
    private static string DetermineVehicleStatus(TelemetryDataReceivedEvent notification)
    {
        if (notification.Speed.KilometersPerHour > 0)
        {
            return "Moving";
        }

        // Check if fuel level is critically low
        if (notification.FuelLevel != null && notification.FuelLevel.Percentage < 10)
        {
            return "Low Fuel";
        }

        // Check how recent the data is
        var dataAge = DateTime.UtcNow - notification.Timestamp;
        if (dataAge.TotalMinutes > 30)
        {
            return "Offline";
        }

        return "Idle";
    }
}

/// <summary>
/// Simplified DTO for real-time telemetry data transmission
/// </summary>
public class TelemetryDataDto
{
    public Guid VehicleId { get; set; }
    public FleetXQ.Application.Features.Vehicles.DTOs.LocationDto Location { get; set; } = null!;
    public decimal SpeedKmh { get; set; }
    public decimal SpeedMph { get; set; }
    public decimal? FuelLevelPercentage { get; set; }
    public decimal CurrentMileage { get; set; }
    public bool IsMoving { get; set; }
    public DateTime Timestamp { get; set; }
    public decimal? EngineTemperature { get; set; }
    public int? RPM { get; set; }
    public Dictionary<string, object>? AdditionalData { get; set; }
}
